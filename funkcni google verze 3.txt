import os
import sys
import math
import argparse
import numpy as np
from pathlib import Path
from typing import Optional, Union, Tuple, Dict

# Set CUDA debugging environment variables BEFORE importing torch
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ['TORCH_USE_CUDA_DSA'] = '1'

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset

# Clear any existing CUDA cache before starting
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    torch.cuda.synchronize()

from accelerate import Accelerator, DistributedDataParallelKwargs
from accelerate.utils import set_seed
from huggingface_hub import hf_hub_download

# Data directory setup
SCRIPT_DIR = Path(__file__).parent if "__file__" in globals() else Path(".")
DATA_DIR = SCRIPT_DIR / "fineweb10B"

def download_fineweb_data(num_chunks: int = 3):
    """Download Fineweb10B dataset from HuggingFace"""
    print(f"Downloading Fineweb10B dataset (up to {num_chunks} train chunks)...")
    
    def get(fname):
        local_dir = str(DATA_DIR)
        os.makedirs(local_dir, exist_ok=True)
        if not os.path.exists(os.path.join(local_dir, fname)):
            print(f"Downloading {fname}...")
            hf_hub_download(
                repo_id="kjj0/fineweb10B-gpt2",
                filename=fname,
                repo_type="dataset",
                local_dir=local_dir,
            )
        else:
            print(f"{fname} already exists, skipping download")
    
    # Download validation data
    get("fineweb_val_%06d.bin" % 0)
    
    # Download training data chunks
    for i in range(1, min(num_chunks + 1, 4)): 
        get("fineweb_train_%06d.bin" % i)
    
    print(f"Dataset download complete! Files saved to {DATA_DIR}")

# =============================================================================
# SIMPLE DATASET FOR DEBUGGING
# =============================================================================

class DebugBinDataset(Dataset):
    """Simplified dataset for debugging CUDA issues"""
    def __init__(self, data_dir: str, split: str, seq_len: int, vocab_size: int = 50257):
        super().__init__()
        self.seq_len = seq_len
        self.vocab_size = vocab_size
        self.data_dir = Path(data_dir)
        
        if split == "train":
            pattern = "fineweb_train_*.bin"
            samples_per_file = 20000 
            max_total_samples = 100000 
        elif split == "val":
            pattern = "fineweb_val_*.bin"
            samples_per_file = 1000 
            max_total_samples = 5000
        else:
            raise ValueError(f"Unknown split: {split}")
        
        self.bin_files = sorted(self.data_dir.glob(pattern))
        if not self.bin_files:
            print(f"Warning: No files found: {self.data_dir}/{pattern}")
            print("Creating synthetic data for debugging...")
            self.use_synthetic = True
            self.total_samples = 1000 if split == "train" else 100
        else:
            self.use_synthetic = False
            self.total_samples = min(max_total_samples, len(self.bin_files) * samples_per_file)
            print(f"Found {len(self.bin_files)} files for {split}")
        
        print(f"Total {split} samples: {self.total_samples}")
        
        if not self.use_synthetic and self.bin_files:
            try:
                print(f"Checking first file: {self.bin_files}")
                test_data = np.memmap(str(self.bin_files), dtype=np.uint16, mode='r')
                print(f"File size: {len(test_data)} tokens")
                print(f"Min token: {test_data[:1000].min()}, Max token: {test_data[:1000].max()}")
                
                if test_data[:1000].max() >= vocab_size:
                    print(f"WARNING: Found tokens >= vocab_size ({vocab_size})")
                    print("Will clamp tokens to valid range")
                    
            except Exception as e:
                print(f"Error checking file: {e}")
                self.use_synthetic = True
    
    def __len__(self):
        return self.total_samples
    
    def __getitem__(self, idx):
        if self.use_synthetic:
            x = torch.randint(0, min(1000, self.vocab_size), (self.seq_len,), dtype=torch.long)
            y = torch.randint(0, min(1000, self.vocab_size), (self.seq_len,), dtype=torch.long)
            return x, y
        
        file_idx = idx % len(self.bin_files)
        
        try:
            file_data = np.memmap(str(self.bin_files[file_idx]), dtype=np.uint16, mode='r')
            max_start = max(0, len(file_data) - self.seq_len - 1)

            if max_start == 0:
                offset = 0
            else:
                effective_idx_in_file = idx // len(self.bin_files) 
                offset = (effective_idx_in_file * 1234567) % max_start

            x_data = file_data[offset:offset + self.seq_len].astype(np.int64)
            y_data = file_data[offset + 1:offset + self.seq_len + 1].astype(np.int64)
            
            x = torch.from_numpy(x_data)
            y = torch.from_numpy(y_data)
            
            x = torch.clamp(x, min=0, max=self.vocab_size - 1)
            y = torch.clamp(y, min=0, max=self.vocab_size - 1)
            
            if len(x) < self.seq_len:
                pad_len = self.seq_len - len(x)
                x = torch.cat([x, torch.zeros(pad_len, dtype=torch.long)])
                y = torch.cat([y, torch.zeros(pad_len, dtype=torch.long)])
            
            return x, y
            
        except Exception as e:
            print(f"Error reading file {self.bin_files[file_idx]} at offset {offset if 'offset' in locals() else 'unknown'}: {e}")
            x = torch.randint(0, min(1000, self.vocab_size), (self.seq_len,), dtype=torch.long)
            y = torch.randint(0, min(1000, self.vocab_size), (self.seq_len,), dtype=torch.long)
            return x, y

# =============================================================================
# SIMPLE MODEL FOR DEBUGGING
# =============================================================================

class SimpleGPT2(nn.Module):
    def __init__(self, vocab_size: int, embed_dim: int, num_layers: int, 
                 num_heads: int, max_length: int, dropout: float = 0.1):
        super().__init__()
        self.vocab_size = vocab_size
        self.embed_dim = embed_dim
        self.max_length = max_length
        
        self.token_embedding = nn.Embedding(vocab_size, embed_dim)
        self.position_embedding = nn.Embedding(max_length, embed_dim)
        self.dropout = nn.Dropout(dropout)
        
        self.blocks = nn.ModuleList([
            nn.TransformerEncoderLayer(
                d_model=embed_dim,
                nhead=num_heads,
                dim_feedforward=4 * embed_dim,
                dropout=dropout,
                batch_first=True,
                norm_first=True 
            ) for _ in range(num_layers)
        ])
        
        self.ln_f = nn.LayerNorm(embed_dim)
        self.lm_head = nn.Linear(embed_dim, vocab_size, bias=False)
        
        self.lm_head.weight = self.token_embedding.weight
        self.apply(self._init_weights)
        print(f"Model initialized: vocab_size={vocab_size}, embed_dim={embed_dim}, layers={num_layers}")
        
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
    
    def forward(self, input_ids: torch.Tensor, targets: Optional[torch.Tensor] = None):
        B, T = input_ids.shape
        device = input_ids.device
        
        if input_ids.max() >= self.vocab_size:
            input_ids = torch.clamp(input_ids, min=0, max=self.vocab_size - 1)
        if input_ids.min() < 0:
            input_ids = torch.clamp(input_ids, min=0, max=self.vocab_size - 1)
        
        try:
            token_emb = self.token_embedding(input_ids)
        except Exception as e:
            print(f"Token embedding error: {e}")
            print(f"Input shape: {input_ids.shape}, dtype: {input_ids.dtype}, min: {input_ids.min()}, max: {input_ids.max()}")
            print(f"Vocab size: {self.vocab_size}")
            raise
            
        pos_ids = torch.arange(T, device=device).unsqueeze(0) 
        pos_emb = self.position_embedding(pos_ids)
        
        x = self.dropout(token_emb + pos_emb)
        
        causal_mask = nn.Transformer.generate_square_subsequent_mask(T, device=device)

        for block in self.blocks:
            x = block(x, src_mask=causal_mask) 
        
        x = self.ln_f(x)
        logits = self.lm_head(x)
        
        if targets is not None:
            targets = torch.clamp(targets, min=0, max=self.vocab_size - 1)
            loss = F.cross_entropy(
                logits.view(-1, self.vocab_size),
                targets.view(-1),
                ignore_index=-100 
            )
            return loss, {'loss': loss.item()}
        
        return logits

# =============================================================================
# EVALUATION FUNCTION
# =============================================================================
@torch.no_grad() 
def evaluate(model, val_loader, accelerator, device_for_no_accel):
    model.eval()
    total_loss = 0
    num_batches = 0
    max_eval_batches = 50 

    for batch_idx, batch in enumerate(val_loader):
        if batch_idx >= max_eval_batches:
            break
        x, y = batch
        if accelerator is None: 
            x, y = x.to(device_for_no_accel), y.to(device_for_no_accel)
        
        loss, loss_dict = model(x, y)
        total_loss += loss.item() 
        num_batches += 1
            
    if num_batches == 0:
        return float('inf')
    avg_loss = total_loss / num_batches
    return avg_loss

# =============================================================================
# MAIN TRAINING FUNCTION
# =============================================================================

def main():
    parser = argparse.ArgumentParser(description="Debug OpenCAP Training")
    
    parser.add_argument("--data_dir", type=str, default=str(DATA_DIR))
    parser.add_argument("--download_data", action="store_true")
    parser.add_argument("--num_data_chunks", type=int, default=3, help="Number of training data chunks to download (1-3).")
    
    parser.add_argument("--model_size", choices=["nano", "tiny"], default="nano")
    
    parser.add_argument("--seq_len", type=int, default=64)
    parser.add_argument("--lr", type=float, default=3e-4)
    parser.add_argument("--batch", type=int, default=1) 
    parser.add_argument("--grad_accum", type=int, default=8)
    parser.add_argument("--iters", type=int, default=5000, help="Total training iterations.")
    parser.add_argument("--eval_interval", type=int, default=250, help="Evaluate on validation set every N iterations.")
    parser.add_argument("--log_interval", type=int, default=50, help="Log training loss every N iterations.")

    parser.add_argument("--seed", type=int, default=42)
    
    if "COLAB_GPU" in os.environ or "VSCODE_NOTEBOOK_CELL_LIST" in os.environ:
        print("🔧 Colab/VSCode Notebook detected: using default args for a potentially longer run.")
        try:
            known_args, _ = parser.parse_known_args()
            args = known_args
        except:
            args = parser.parse_args([])
    else:
        args = parser.parse_args()
    
    if args.download_data or not Path(args.data_dir).exists() or not list(Path(args.data_dir).glob("fineweb_train_*.bin")):
        download_fineweb_data(num_chunks=args.num_data_chunks)
    
    device_for_no_accel = None 
    if torch.cuda.is_available():
        print(f"CUDA available: {torch.cuda.get_device_name()}")
        print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        try:
            print("Testing basic CUDA operations...")
            test_tensor = torch.randn(10, 10).cuda()
            _ = test_tensor @ test_tensor.T
            print("Basic CUDA operations: OK")
        except Exception as e:
            print(f"CUDA test failed: {e}")
            print("Falling back to CPU for model and data if accelerator not used.")
            os.environ['CUDA_VISIBLE_DEVICES'] = '' 
            device_for_no_accel = torch.device("cpu") 
    else:
        print("CUDA not available. Using CPU.")
        device_for_no_accel = torch.device("cpu")

    try:
        set_seed(args.seed)
        print(f"Seed set to {args.seed}")
    except Exception as e:
        print(f"Warning: Could not set seed via accelerate: {e}")
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(args.seed)
    
    size2cfg = {
        "nano": dict(layers=2, heads=2, embed=128, max_length=args.seq_len + 10), 
        "tiny": dict(layers=3, heads=4, embed=192, max_length=args.seq_len + 10),
    }[args.model_size]
    
    accelerator = None
    try:
        ddp_kwargs = DistributedDataParallelKwargs(find_unused_parameters=False) 
        accelerator = Accelerator(
            mixed_precision="no",
            gradient_accumulation_steps=args.grad_accum,
            kwargs_handlers=[ddp_kwargs] 
        )
        print(f"Accelerator initialized on device: {accelerator.device}")
        if device_for_no_accel is None and accelerator.device.type == 'cpu': 
             device_for_no_accel = torch.device("cpu")
        elif device_for_no_accel is None: 
             device_for_no_accel = accelerator.device 
    except Exception as e:
        print(f"Accelerator error: {e}")
        print("Running without accelerator.")
        if device_for_no_accel is None: 
             device_for_no_accel = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    vocab_size = 50257
    
    try:
        print("Loading datasets...")
        train_ds = DebugBinDataset(args.data_dir, "train", args.seq_len, vocab_size)
        val_ds = DebugBinDataset(args.data_dir, "val", args.seq_len, vocab_size)
    except Exception as e:
        print(f"Dataset error: {e}")
        return
    
    train_loader = DataLoader(
        train_ds, batch_size=args.batch, shuffle=True, 
        pin_memory=False, num_workers=0, drop_last=True 
    )
    val_loader = DataLoader(
        val_ds, batch_size=args.batch, shuffle=False,
        pin_memory=False, num_workers=0, drop_last=False 
    )
    
    model = SimpleGPT2(
        vocab_size=vocab_size,
        embed_dim=size2cfg["embed"],
        num_layers=size2cfg["layers"],
        num_heads=size2cfg["heads"],
        max_length=max(size2cfg["max_length"], args.seq_len + 2), 
        dropout=0.0 
    )
    
    total_params = sum(p.numel() for p in model.parameters())
    print(f"Model parameters: {total_params:,}")
    
    optimizer = torch.optim.AdamW(model.parameters(), lr=args.lr)
    
    if accelerator:
        model, optimizer, train_loader, val_loader = accelerator.prepare(
            model, optimizer, train_loader, val_loader
        )
    else:
        model = model.to(device_for_no_accel)
    
    print("\n🧪 Testing forward pass...")
    try:
        with torch.no_grad(): 
            test_batch_iter = iter(train_loader) 
            x, y = next(test_batch_iter)
            if accelerator is None: 
                 x, y = x.to(device_for_no_accel), y.to(device_for_no_accel)

            print(f"Test batch shape: x={x.shape}, y={y.shape}")
            
            loss, loss_dict = model(x, y)
                
            print(f"Test forward pass successful! Loss: {loss.item():.4f}")
    except Exception as e:
        print(f"❌ Forward pass failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n🚀 Starting training...")
    model.train() 
    
    train_iter = iter(train_loader)

    for step in range(args.iters):
        try:
            try:
                batch = next(train_iter)
            except StopIteration: 
                print("Train dataloader exhausted, re-initializing.")
                train_iter = iter(train_loader) 
                batch = next(train_iter)
            
            x, y = batch
            if accelerator is None: 
                 x, y = x.to(device_for_no_accel), y.to(device_for_no_accel)
            
            current_loss = -1.0 
            if accelerator:
                with accelerator.accumulate(model):
                    loss, loss_dict = model(x, y)
                    accelerator.backward(loss)
                    optimizer.step()
                    optimizer.zero_grad()
                    current_loss = loss_dict['loss']
            else: 
                loss, loss_dict = model(x, y)
                effective_loss = loss / args.grad_accum 
                effective_loss.backward()

                if (step + 1) % args.grad_accum == 0:
                    optimizer.step()
                    optimizer.zero_grad()
                current_loss = loss_dict['loss'] 


            if step > 0 and step % args.eval_interval == 0:
                val_loss = evaluate(model, val_loader, accelerator, device_for_no_accel)
                model.train() 
                
                print(f"Step {step:>5}/{args.iters} | Train Loss {current_loss:.4f} | Val Loss {val_loss:.4f}")
                if val_loss <= 4.01: 
                    print(f"Target validation loss ({val_loss:.4f}) reached or surpassed 4.01. Stopping early.")
                    break

            elif step % args.log_interval == 0:
                print(f"Step {step:>5}/{args.iters} | Train Loss {current_loss:.4f}")
            
            if step > 0 and step % (args.log_interval * 10) == 0 and torch.cuda.is_available(): 
                torch.cuda.empty_cache()
                
        except Exception as e:
            print(f"Training error at step {step}: {e}")
            if "out of memory" in str(e).lower() and torch.cuda.is_available():
                print("Attempting to clear CUDA cache...")
                torch.cuda.empty_cache()
                print("CUDA cache cleared. Skipping this step and continuing. Reduce batch size or seq_len if this persists.")
                if accelerator: optimizer.zero_grad() 
                else: 
                    if (step +1) % args.grad_accum != 0 : 
                         optimizer.zero_grad()
                continue
            else:
                import traceback
                traceback.print_exc()
                break
    
    print("🎬 Training completed!")

if __name__ == "__main__":
    main()