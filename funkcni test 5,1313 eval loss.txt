import os
import sys
import math
import argparse
import numpy as np
from pathlib import Path
from typing import Optional, Union, Tuple, Dict
import time

# Set CUDA debugging environment variables BEFORE importing torch
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ['TORCH_USE_CUDA_DSA'] = '1'

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset

# Clear any existing CUDA cache before starting
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    torch.cuda.synchronize()

from accelerate import Accelerator, DistributedDataParallelKwargs
from accelerate.utils import set_seed
from huggingface_hub import hf_hub_download

# Data directory setup
SCRIPT_DIR = Path(__file__).parent if "__file__" in globals() else Path(".")
DATA_DIR = SCRIPT_DIR / "fineweb10B"

def download_fineweb_data(num_chunks: int = 10):
    """Download Fineweb10B dataset from HuggingFace - increased chunks for more data"""
    print("Downloading Fineweb10B dataset...")
    
    def get(fname):
        local_dir = str(DATA_DIR)
        os.makedirs(local_dir, exist_ok=True)
        if not os.path.exists(os.path.join(local_dir, fname)):
            print(f"Downloading {fname}...")
            hf_hub_download(
                repo_id="kjj0/fineweb10B-gpt2",
                filename=fname,
                repo_type="dataset",
                local_dir=local_dir,
            )
        else:
            print(f"{fname} already exists, skipping download")
    
    # Download validation data
    get("fineweb_val_%06d.bin" % 0)
    
    # Download training data chunks - increased for better training
    for i in range(1, min(num_chunks + 1, 11)):
        get("fineweb_train_%06d.bin" % i)
    
    print(f"Dataset download complete! Files saved to {DATA_DIR}")

# =============================================================================
# IMPROVED DATASET WITH PROPER SAMPLING
# =============================================================================

class ImprovedBinDataset(Dataset):
    """Improved dataset with better sampling and no token clamping issues"""
    def __init__(self, data_dir: str, split: str, seq_len: int, vocab_size: int = 50257):
        super().__init__()
        self.seq_len = seq_len
        self.vocab_size = vocab_size
        self.data_dir = Path(data_dir)
        
        # Find binary files
        if split == "train":
            pattern = "fineweb_train_*.bin"
        elif split == "val":
            pattern = "fineweb_val_*.bin"
        else:
            raise ValueError(f"Unknown split: {split}")
        
        self.bin_files = sorted(self.data_dir.glob(pattern))
        if not self.bin_files:
            raise ValueError(f"No files found: {self.data_dir}/{pattern}")
            
        print(f"Found {len(self.bin_files)} files for {split}")
        
        # Calculate total tokens available
        self.file_sizes = []
        self.cumulative_sizes = [0]
        total_tokens = 0
        
        for file_path in self.bin_files:
            file_size = os.path.getsize(file_path) // 2  # uint16 = 2 bytes
            self.file_sizes.append(file_size)
            total_tokens += file_size
            self.cumulative_sizes.append(total_tokens)
        
        # Calculate number of complete sequences we can extract
        self.total_sequences = total_tokens // (seq_len + 1)
        print(f"Total tokens: {total_tokens:,}, Total sequences: {self.total_sequences:,}")
        
    def __len__(self):
        return self.total_sequences
    
    def __getitem__(self, idx):
        # Calculate which file contains this sequence
        seq_start = idx * (self.seq_len + 1)
        
        # Binary search to find the right file
        file_idx = 0
        for i, cum_size in enumerate(self.cumulative_sizes[1:]):
            if seq_start < cum_size:
                file_idx = i
                break
        
        # Load the memory map
        file_path = self.bin_files[file_idx]
        data = np.memmap(str(file_path), dtype=np.uint16, mode='r')
        
        # Calculate offset within this file
        offset_in_file = seq_start - self.cumulative_sizes[file_idx]
        
        # Handle edge case where sequence spans multiple files
        if offset_in_file + self.seq_len + 1 > len(data):
            # Use a different random position within the same file
            offset_in_file = np.random.randint(0, len(data) - self.seq_len - 1)
        
        # Extract sequence
        chunk = data[offset_in_file:offset_in_file + self.seq_len + 1].astype(np.int64)
        
        # Clamp tokens to valid range (handle the vocab_size issue)
        chunk = np.clip(chunk, 0, self.vocab_size - 1)
        
        x = torch.from_numpy(chunk[:-1]).long()
        y = torch.from_numpy(chunk[1:]).long()
        
        return x, y

# =============================================================================
# IMPROVED GPT-2 MODEL WITH BETTER ARCHITECTURE
# =============================================================================

class ImprovedGPT2(nn.Module):
    """Improved GPT-2 with better initialization and architecture"""
    def __init__(self, vocab_size: int, embed_dim: int, num_layers: int, 
                 num_heads: int, max_length: int, dropout: float = 0.1):
        super().__init__()
        self.vocab_size = vocab_size
        self.embed_dim = embed_dim
        self.max_length = max_length
        
        # Embeddings with better initialization
        self.token_embedding = nn.Embedding(vocab_size, embed_dim)
        self.position_embedding = nn.Embedding(max_length, embed_dim)
        self.embed_dropout = nn.Dropout(dropout)
        
        # Layer normalization for embeddings (pre-norm)
        self.embed_ln = nn.LayerNorm(embed_dim)
        
        # Transformer blocks with better configuration
        self.blocks = nn.ModuleList()
        for _ in range(num_layers):
            block = nn.TransformerEncoderLayer(
                d_model=embed_dim,
                nhead=num_heads,
                dim_feedforward=4 * embed_dim,
                dropout=dropout,
                activation='gelu',  # Better activation
                batch_first=True,
                norm_first=True  # Pre-norm for stability
            )
            self.blocks.append(block)
        
        # Output
        self.ln_f = nn.LayerNorm(embed_dim)
        self.lm_head = nn.Linear(embed_dim, vocab_size, bias=False)
        
        # Tie weights
        self.lm_head.weight = self.token_embedding.weight
        
        # Initialize weights with scaled initialization
        self.apply(self._init_weights)
        
        # Scale embeddings
        with torch.no_grad():
            self.token_embedding.weight.mul_(0.01)
            self.position_embedding.weight.mul_(0.01)
        
        print(f"Model initialized: vocab_size={vocab_size}, embed_dim={embed_dim}, layers={num_layers}")
        
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            # Scaled initialization based on layer depth
            std = 0.02
            if hasattr(module, 'weight'):
                torch.nn.init.normal_(module.weight, mean=0.0, std=std)
            if hasattr(module, 'bias') and module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            if hasattr(module, 'bias'):
                torch.nn.init.zeros_(module.bias)
            if hasattr(module, 'weight'):
                torch.nn.init.ones_(module.weight)
    
    def forward(self, input_ids: torch.Tensor, targets: Optional[torch.Tensor] = None):
        B, T = input_ids.shape
        device = input_ids.device
        
        # Token embeddings
        token_emb = self.token_embedding(input_ids)
        
        # Position embeddings
        pos_ids = torch.arange(T, device=device, dtype=torch.long).unsqueeze(0).expand(B, -1)
        pos_emb = self.position_embedding(pos_ids)
        
        # Combine and normalize
        x = token_emb + pos_emb
        x = self.embed_ln(x)
        x = self.embed_dropout(x)
        
        # Create causal mask once
        causal_mask = torch.triu(torch.ones(T, T, device=device), diagonal=1).bool()
        
        # Apply transformer blocks
        for block in self.blocks:
            x = block(x, src_mask=causal_mask, is_causal=True)
        
        # Final layer norm
        x = self.ln_f(x)
        
        # Output projection
        logits = self.lm_head(x)
        
        loss = None
        if targets is not None:
            # Compute loss with label smoothing
            loss = F.cross_entropy(
                logits.view(-1, self.vocab_size),
                targets.view(-1),
                label_smoothing=0.1  # Helps with generalization
            )
        
        return logits, loss

# =============================================================================
# COSINE LEARNING RATE SCHEDULER
# =============================================================================

def get_cosine_schedule_with_warmup(optimizer, num_warmup_steps, num_training_steps, min_lr_ratio=0.1):
    """Cosine learning rate schedule with warmup"""
    def lr_lambda(current_step):
        if current_step < num_warmup_steps:
            return float(current_step) / float(max(1, num_warmup_steps))
        progress = float(current_step - num_warmup_steps) / float(max(1, num_training_steps - num_warmup_steps))
        return max(min_lr_ratio, 0.5 * (1.0 + math.cos(math.pi * progress)))
    
    return torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)

# =============================================================================
# MAIN TRAINING FUNCTION
# =============================================================================

def main():
    parser = argparse.ArgumentParser(description="Improved GPT-2 Training")
    
    # Data configuration
    parser.add_argument("--data_dir", type=str, default=str(DATA_DIR))
    parser.add_argument("--download_data", action="store_true")
    parser.add_argument("--num_chunks", type=int, default=10)
    
    # Model configuration
    parser.add_argument("--model_size", choices=["small", "medium", "large"], default="medium")
    
    # Training configuration
    parser.add_argument("--seq_len", type=int, default=512)
    parser.add_argument("--lr", type=float, default=6e-4)
    parser.add_argument("--min_lr", type=float, default=6e-5)
    parser.add_argument("--batch", type=int, default=8)
    parser.add_argument("--grad_accum", type=int, default=8)
    parser.add_argument("--iters", type=int, default=10000)
    parser.add_argument("--warmup_iters", type=int, default=500)
    parser.add_argument("--eval_interval", type=int, default=500)
    parser.add_argument("--eval_iters", type=int, default=50)
    parser.add_argument("--log_interval", type=int, default=50)
    
    # Optimization
    parser.add_argument("--weight_decay", type=float, default=0.1)
    parser.add_argument("--grad_clip", type=float, default=1.0)
    
    # System
    parser.add_argument("--seed", type=int, default=42)
    parser.add_argument("--compile", action="store_true", help="Use torch.compile")
    
    # Parse arguments
    if "COLAB_GPU" in os.environ or "VSCODE_NOTEBOOK_CELL_LIST" in os.environ:
        args = parser.parse_args([])
        print("🔧 Colab detected: using default settings")
    else:
        args = parser.parse_args()
    
    # Download data if needed
    if args.download_data or not Path(args.data_dir).exists():
        download_fineweb_data(num_chunks=args.num_chunks)
    
    # Set seed
    set_seed(args.seed)
    
    # Model configurations - proper sizes
    model_configs = {
        "small": dict(layers=6, heads=6, embed=384),      # ~20M params
        "medium": dict(layers=8, heads=8, embed=512),     # ~50M params
        "large": dict(layers=12, heads=12, embed=768),    # ~125M params
    }
    
    config = model_configs[args.model_size]
    
    # Initialize accelerator
    accelerator = Accelerator(
        mixed_precision="fp16" if torch.cuda.is_available() else "no",
        gradient_accumulation_steps=args.grad_accum
    )
    
    # Datasets
    vocab_size = 50257
    
    print("Loading datasets...")
    train_ds = ImprovedBinDataset(args.data_dir, "train", args.seq_len, vocab_size)
    val_ds = ImprovedBinDataset(args.data_dir, "val", args.seq_len, vocab_size)
    
    # Data loaders
    train_loader = DataLoader(
        train_ds, 
        batch_size=args.batch, 
        shuffle=True,
        num_workers=2 if not ("COLAB_GPU" in os.environ) else 0,
        pin_memory=torch.cuda.is_available(),
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_ds, 
        batch_size=args.batch, 
        shuffle=False,
        num_workers=2 if not ("COLAB_GPU" in os.environ) else 0,
        pin_memory=torch.cuda.is_available(),
        drop_last=True
    )
    
    # Model
    model = ImprovedGPT2(
        vocab_size=vocab_size,
        embed_dim=config["embed"],
        num_layers=config["layers"],
        num_heads=config["heads"],
        max_length=args.seq_len,
        dropout=0.1
    )
    
    # Compile model if requested
    if args.compile and hasattr(torch, 'compile'):
        print("Compiling model...")
        model = torch.compile(model)
    
    total_params = sum(p.numel() for p in model.parameters())
    print(f"Model parameters: {total_params:,}")
    
    # Optimizer with weight decay
    def configure_optimizers(model, weight_decay, learning_rate):
        decay_params = []
        no_decay_params = []
        
        for name, param in model.named_parameters():
            if param.requires_grad:
                if 'bias' in name or 'ln' in name or 'norm' in name:
                    no_decay_params.append(param)
                else:
                    decay_params.append(param)
        
        return torch.optim.AdamW([
            {'params': decay_params, 'weight_decay': weight_decay},
            {'params': no_decay_params, 'weight_decay': 0.0}
        ], lr=learning_rate, betas=(0.9, 0.95), eps=1e-8)
    
    optimizer = configure_optimizers(model, args.weight_decay, args.lr)
    
    # Learning rate scheduler
    scheduler = get_cosine_schedule_with_warmup(
        optimizer, 
        num_warmup_steps=args.warmup_iters,
        num_training_steps=args.iters,
        min_lr_ratio=args.min_lr / args.lr
    )
    
    # Prepare with accelerator
    model, optimizer, train_loader, val_loader, scheduler = accelerator.prepare(
        model, optimizer, train_loader, val_loader, scheduler
    )
    
    # Training loop
    print("\n🚀 Starting training...")
    model.train()
    
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    
    start_time = time.time()
    
    for step in range(args.iters):
        # Training step
        for micro_step in range(args.grad_accum):
            try:
                batch = next(iter(train_loader))
                x, y = batch
                
                with accelerator.accumulate(model):
                    logits, loss = model(x, y)
                    accelerator.backward(loss)
                    
                    if accelerator.sync_gradients:
                        # Gradient clipping
                        accelerator.clip_grad_norm_(model.parameters(), args.grad_clip)
                    
                    optimizer.step()
                    scheduler.step()
                    optimizer.zero_grad()
                
                train_losses.append(loss.item())
                
            except StopIteration:
                # Restart data loader
                train_loader = DataLoader(
                    train_ds, 
                    batch_size=args.batch, 
                    shuffle=True,
                    num_workers=2 if not ("COLAB_GPU" in os.environ) else 0,
                    pin_memory=torch.cuda.is_available(),
                    drop_last=True
                )
                train_loader = accelerator.prepare(train_loader)
        
        # Logging
        if (step + 1) % args.log_interval == 0:
            avg_loss = np.mean(train_losses[-args.log_interval:])
            elapsed = time.time() - start_time
            current_lr = scheduler.get_last_lr()[0]
            print(f"Step {step+1:>5}/{args.iters} | Loss {avg_loss:.4f} | LR {current_lr:.2e} | Time {elapsed:.1f}s")
        
        # Evaluation
        if (step + 1) % args.eval_interval == 0:
            model.eval()
            eval_losses = []
            
            with torch.no_grad():
                for eval_step, batch in enumerate(val_loader):
                    if eval_step >= args.eval_iters:
                        break
                    x, y = batch
                    logits, loss = model(x, y)
                    eval_losses.append(loss.item())
            
            val_loss = np.mean(eval_losses)
            val_losses.append(val_loss)
            
            print(f"\n📊 Validation at step {step+1}: Loss = {val_loss:.4f}")
            
            # Save best model
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                if accelerator.is_main_process:
                    print(f"💾 New best model! Validation loss: {val_loss:.4f}")
                    # Save checkpoint
                    checkpoint = {
                        'model_state_dict': accelerator.unwrap_model(model).state_dict(),
                        'optimizer_state_dict': optimizer.state_dict(),
                        'scheduler_state_dict': scheduler.state_dict(),
                        'step': step + 1,
                        'val_loss': val_loss,
                        'config': config,
                        'args': vars(args)
                    }
                    torch.save(checkpoint, 'best_model.pt')
            
            model.train()
            
            # Early stopping check
            if val_loss < 4.0:
                print(f"\n🎯 Target validation loss reached: {val_loss:.4f}")
                break
        
        # Clear cache periodically
        if (step + 1) % 1000 == 0 and torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    print(f"\n🎬 Training completed! Best validation loss: {best_val_loss:.4f}")

if __name__ == "__main__":
    main()