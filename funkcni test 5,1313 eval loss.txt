import os
import sys
import math
import argparse
import numpy as np
from pathlib import Path
from typing import Optional, Union, Tuple, Dict
import time

# Set CUDA debugging environment variables BEFORE importing torch
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ['TORCH_USE_CUDA_DSA'] = '1'

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
from torch.nn.utils.rnn import pad_sequence

# Clear any existing CUDA cache before starting
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    torch.cuda.synchronize()

from accelerate import Accelerator, DistributedDataParallelKwargs
from accelerate.utils import set_seed
from huggingface_hub import hf_hub_download

# Data directory setup
SCRIPT_DIR = Path(__file__).parent if "__file__" in globals() else Path(".")
DATA_DIR = SCRIPT_DIR / "fineweb10B"

def download_fineweb_data(num_chunks: int = 10):
    """Download Fineweb10B dataset from HuggingFace - increased chunks for more data"""
    print("Downloading Fineweb10B dataset...")

    def get(fname):
        local_dir = str(DATA_DIR)
        os.makedirs(local_dir, exist_ok=True)
        if not os.path.exists(os.path.join(local_dir, fname)):
            print(f"Downloading {fname}...")
            hf_hub_download(
                repo_id="kjj0/fineweb10B-gpt2",
                filename=fname,
                repo_type="dataset",
                local_dir=local_dir,
            )
        else:
            print(f"{fname} already exists, skipping download")

    # Download validation data
    get("fineweb_val_%06d.bin" % 0)

    # Download training data chunks - increased for better training
    for i in range(1, min(num_chunks + 1, 11)):
        get("fineweb_train_%06d.bin" % i)

    print(f"Dataset download complete! Files saved to {DATA_DIR}")

# =============================================================================
# IMPROVED DATASET WITH PROPER SAMPLING
# =============================================================================

class ImprovedBinDataset(Dataset):
    """Improved dataset with better sampling and no token clamping issues"""
    def __init__(self, data_dir: str, split: str, seq_len: int, vocab_size: int = 50257):
        super().__init__()
        self.seq_len = seq_len
        self.vocab_size = vocab_size
        self.data_dir = Path(data_dir)

        # Find binary files
        if split == "train":
            pattern = "fineweb_train_*.bin"
        elif split == "val":
            pattern = "fineweb_val_*.bin"
        else:
            raise ValueError(f"Unknown split: {split}")

        self.bin_files = sorted(self.data_dir.glob(pattern))
        if not self.bin_files:
            raise ValueError(f"No files found: {self.data_dir}/{pattern}")

        print(f"Found {len(self.bin_files)} files for {split}")

        # Calculate total tokens available
        self.file_sizes = []
        self.cumulative_sizes = [0]
        total_tokens = 0

        for file_path in self.bin_files:
            file_size = os.path.getsize(file_path) // 2  # uint16 = 2 bytes
            self.file_sizes.append(file_size)
            total_tokens += file_size
            self.cumulative_sizes.append(total_tokens)

        # Calculate number of complete sequences we can extract
        self.total_sequences = total_tokens // (seq_len + 1)
        print(f"Total tokens: {total_tokens:,}, Total sequences: {self.total_sequences:,}")

    def __len__(self):
        return self.total_sequences

    def __getitem__(self, idx):
        # Calculate which file contains this sequence
        seq_start = idx * (self.seq_len + 1)

        # Binary search to find the right file
        file_idx = 0
        for i, cum_size in enumerate(self.cumulative_sizes[1:]):
            if seq_start < cum_size:
                file_idx = i
                break

        # Load the memory map
        file_path = self.bin_files[file_idx]
        data = np.memmap(str(file_path), dtype=np.uint16, mode='r')

        # Calculate offset within this file
        offset_in_file = seq_start - self.cumulative_sizes[file_idx]

        # Handle edge case where sequence spans multiple files
        if offset_in_file + self.seq_len + 1 > len(data):
            # Use a different random position within the same file
            offset_in_file = np.random.randint(0, len(data) - self.seq_len - 1)

        # Extract sequence
        chunk = data[offset_in_file:offset_in_file + self.seq_len + 1].astype(np.int64)

        # Clamp tokens to valid range (handle the vocab_size issue)
        chunk = np.clip(chunk, 0, self.vocab_size - 1)

        x = torch.from_numpy(chunk[:-1]).long()
        y = torch.from_numpy(chunk[1:]).long()

        return x, y

# =============================================================================
# ADVANCED COMPONENTS FOR IMPROVED ARCHITECTURE
# =============================================================================

class ConditionalEmbedding(nn.Module):
    """Advanced conditional embeddings inspired by DeepSeek and T5"""
    def __init__(self, vocab_size: int, embed_dim: int, max_length: int,
                 num_tasks: int = 8, num_segments: int = 4):
        super().__init__()
        self.embed_dim = embed_dim

        # Core embeddings
        self.token_embedding = nn.Embedding(vocab_size, embed_dim)
        self.position_embedding = nn.Embedding(max_length, embed_dim)

        # Conditional embeddings
        self.task_embedding = nn.Embedding(num_tasks, embed_dim)
        self.segment_embedding = nn.Embedding(num_segments, embed_dim)

        # Pre-norm layer normalization for stability
        self.embed_ln = nn.LayerNorm(embed_dim)

        # Learnable mixing weights
        self.mixing_weights = nn.Parameter(torch.ones(4) / 4)  # token, pos, task, segment

    def forward(self, input_ids: torch.Tensor, task_ids: Optional[torch.Tensor] = None,
                segment_ids: Optional[torch.Tensor] = None):
        B, T = input_ids.shape
        device = input_ids.device

        # Token embeddings
        token_emb = self.token_embedding(input_ids)

        # Position embeddings
        pos_ids = torch.arange(T, device=device, dtype=torch.long).unsqueeze(0).expand(B, -1)
        pos_emb = self.position_embedding(pos_ids)

        # Task embeddings (default to task 0 if not provided)
        if task_ids is None:
            task_ids = torch.zeros(B, device=device, dtype=torch.long)
        task_emb = self.task_embedding(task_ids).unsqueeze(1).expand(-1, T, -1)

        # Segment embeddings (default to segment 0 if not provided)
        if segment_ids is None:
            segment_ids = torch.zeros(B, T, device=device, dtype=torch.long)
        segment_emb = self.segment_embedding(segment_ids)

        # Weighted combination of embeddings
        weights = F.softmax(self.mixing_weights, dim=0)
        combined_emb = (weights[0] * token_emb +
                       weights[1] * pos_emb +
                       weights[2] * task_emb +
                       weights[3] * segment_emb)

        # Pre-norm for stability
        return self.embed_ln(combined_emb)


class RelativePositionBias(nn.Module):
    """Relative position bias inspired by T5 and DeBERTa"""
    def __init__(self, num_heads: int, max_distance: int = 128):
        super().__init__()
        self.num_heads = num_heads
        self.max_distance = max_distance

        # Learnable relative position embeddings
        self.relative_attention_bias = nn.Embedding(2 * max_distance + 1, num_heads)

    def forward(self, seq_len: int, device: torch.device):
        # Create relative position matrix
        positions = torch.arange(seq_len, device=device)
        relative_positions = positions.unsqueeze(0) - positions.unsqueeze(1)

        # Clamp to max distance
        relative_positions = torch.clamp(relative_positions, -self.max_distance, self.max_distance)
        relative_positions += self.max_distance  # Shift to positive indices

        # Get bias values
        bias = self.relative_attention_bias(relative_positions)  # [seq_len, seq_len, num_heads]
        return bias.permute(2, 0, 1)  # [num_heads, seq_len, seq_len]


class SparseAttention(nn.Module):
    """Sparse attention with dynamic routing and relative position bias"""
    def __init__(self, embed_dim: int, num_heads: int, dropout: float = 0.1,
                 sparsity_ratio: float = 0.1, max_distance: int = 128):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.sparsity_ratio = sparsity_ratio

        assert embed_dim % num_heads == 0, "embed_dim must be divisible by num_heads"

        # Linear projections
        self.q_proj = nn.Linear(embed_dim, embed_dim, bias=False)
        self.k_proj = nn.Linear(embed_dim, embed_dim, bias=False)
        self.v_proj = nn.Linear(embed_dim, embed_dim, bias=False)
        self.out_proj = nn.Linear(embed_dim, embed_dim)

        # Relative position bias
        self.relative_bias = RelativePositionBias(num_heads, max_distance)

        # Sparse routing network
        self.routing_net = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 4),
            nn.ReLU(),
            nn.Linear(embed_dim // 4, 1),
            nn.Sigmoid()
        )

        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor, attn_mask: Optional[torch.Tensor] = None):
        B, T, C = x.shape

        # Compute Q, K, V
        q = self.q_proj(x).view(B, T, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.k_proj(x).view(B, T, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.v_proj(x).view(B, T, self.num_heads, self.head_dim).transpose(1, 2)

        # Compute attention scores
        scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.head_dim)

        # Add relative position bias
        rel_bias = self.relative_bias(T, x.device)
        scores = scores + rel_bias.unsqueeze(0)  # [B, num_heads, T, T]

        # Dynamic sparse routing
        routing_scores = self.routing_net(x).squeeze(-1)  # [B, T]

        # Create sparse mask based on routing scores
        k_sparse = max(1, int(T * self.sparsity_ratio))
        _, top_indices = torch.topk(routing_scores, k=k_sparse, dim=-1)
        sparse_mask = torch.zeros_like(routing_scores, dtype=torch.bool)
        sparse_mask.scatter_(1, top_indices, True)

        # Apply sparse mask to attention (only mask keys, not queries)
        sparse_mask_expanded = sparse_mask.unsqueeze(1).unsqueeze(2)  # [B, 1, 1, T]
        scores = scores.masked_fill(~sparse_mask_expanded, float('-inf'))

        # Apply causal mask if provided
        if attn_mask is not None:
            scores = scores.masked_fill(attn_mask.unsqueeze(0).unsqueeze(0), float('-inf'))

        # Compute attention weights
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)

        # Apply attention to values
        out = torch.matmul(attn_weights, v)
        out = out.transpose(1, 2).contiguous().view(B, T, C)

        return self.out_proj(out)


class SwiGLU(nn.Module):
    """SwiGLU activation function for efficient feed-forward networks"""
    def __init__(self, embed_dim: int, hidden_dim: Optional[int] = None):
        super().__init__()
        hidden_dim = hidden_dim or int(embed_dim * 8/3)  # Standard SwiGLU ratio

        self.gate_proj = nn.Linear(embed_dim, hidden_dim, bias=False)
        self.up_proj = nn.Linear(embed_dim, hidden_dim, bias=False)
        self.down_proj = nn.Linear(hidden_dim, embed_dim, bias=False)

    def forward(self, x: torch.Tensor):
        gate = F.silu(self.gate_proj(x))  # SiLU activation
        up = self.up_proj(x)
        return self.down_proj(gate * up)


class DiffusionBlock(nn.Module):
    """Diffusion-inspired transformer block with denoising capabilities"""
    def __init__(self, embed_dim: int, num_heads: int, dropout: float = 0.1,
                 noise_schedule_steps: int = 1000):
        super().__init__()
        self.embed_dim = embed_dim
        self.noise_schedule_steps = noise_schedule_steps

        # Attention layer with sparse attention
        self.attention = SparseAttention(embed_dim, num_heads, dropout)

        # Feed-forward with SwiGLU
        self.feed_forward = SwiGLU(embed_dim)

        # Layer normalization (pre-norm)
        self.ln1 = nn.LayerNorm(embed_dim)
        self.ln2 = nn.LayerNorm(embed_dim)

        # Noise prediction network
        self.noise_predictor = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 2),
            nn.SiLU(),
            nn.Linear(embed_dim // 2, embed_dim)
        )

        # Time embedding for diffusion steps
        self.time_embedding = nn.Embedding(noise_schedule_steps, embed_dim)

        # Dropout
        self.dropout = nn.Dropout(dropout)

    def add_noise(self, x: torch.Tensor, noise_level: float = 0.1):
        """Add noise to input for diffusion training"""
        noise = torch.randn_like(x) * noise_level
        return x + noise, noise

    def forward(self, x: torch.Tensor, timestep: Optional[int] = None,
                attn_mask: Optional[torch.Tensor] = None):
        # Add time embedding if timestep provided
        if timestep is not None:
            time_emb = self.time_embedding(torch.tensor(timestep, device=x.device))
            x = x + time_emb.unsqueeze(0).unsqueeze(0)

        # Pre-norm attention
        attn_out = self.attention(self.ln1(x), attn_mask)
        x = x + self.dropout(attn_out)

        # Pre-norm feed-forward
        ff_out = self.feed_forward(self.ln2(x))
        x = x + self.dropout(ff_out)

        # Noise prediction for diffusion loss
        predicted_noise = self.noise_predictor(x)

        return x, predicted_noise


class AdvancedTransformerBlock(nn.Module):
    """Advanced transformer block combining sparse attention and diffusion"""
    def __init__(self, embed_dim: int, num_heads: int, dropout: float = 0.1,
                 use_diffusion: bool = True):
        super().__init__()
        self.use_diffusion = use_diffusion

        if use_diffusion:
            self.block = DiffusionBlock(embed_dim, num_heads, dropout)
        else:
            # Standard block with sparse attention and SwiGLU
            self.attention = SparseAttention(embed_dim, num_heads, dropout)
            self.feed_forward = SwiGLU(embed_dim)
            self.ln1 = nn.LayerNorm(embed_dim)
            self.ln2 = nn.LayerNorm(embed_dim)
            self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor, timestep: Optional[int] = None,
                attn_mask: Optional[torch.Tensor] = None):
        if self.use_diffusion:
            return self.block(x, timestep, attn_mask)
        else:
            # Standard transformer block
            attn_out = self.attention(self.ln1(x), attn_mask)
            x = x + self.dropout(attn_out)

            ff_out = self.feed_forward(self.ln2(x))
            x = x + self.dropout(ff_out)

            return x, None  # No predicted noise for standard blocks


# =============================================================================
# IMPROVED GPT-2 MODEL WITH BETTER ARCHITECTURE
# =============================================================================

class ImprovedGPT2(nn.Module):
    """Advanced GPT-2 with conditional embeddings, sparse attention, and diffusion blocks"""
    def __init__(self, vocab_size: int, embed_dim: int, num_layers: int,
                 num_heads: int, max_length: int, dropout: float = 0.1,
                 num_tasks: int = 8, num_segments: int = 4,
                 use_diffusion: bool = True, diffusion_ratio: float = 0.5):
        super().__init__()
        self.vocab_size = vocab_size
        self.embed_dim = embed_dim
        self.max_length = max_length
        self.use_diffusion = use_diffusion
        self.num_layers = num_layers

        # Advanced conditional embeddings
        self.embeddings = ConditionalEmbedding(
            vocab_size=vocab_size,
            embed_dim=embed_dim,
            max_length=max_length,
            num_tasks=num_tasks,
            num_segments=num_segments
        )

        self.embed_dropout = nn.Dropout(dropout)

        # Advanced transformer blocks
        self.blocks = nn.ModuleList()
        num_diffusion_blocks = int(num_layers * diffusion_ratio)

        for i in range(num_layers):
            # Use diffusion blocks for the first portion of layers
            use_diffusion_block = i < num_diffusion_blocks and use_diffusion
            block = AdvancedTransformerBlock(
                embed_dim=embed_dim,
                num_heads=num_heads,
                dropout=dropout,
                use_diffusion=use_diffusion_block
            )
            self.blocks.append(block)

        # Output layers
        self.ln_f = nn.LayerNorm(embed_dim)
        self.lm_head = nn.Linear(embed_dim, vocab_size, bias=False)

        # Tie weights between embeddings and output
        self.lm_head.weight = self.embeddings.token_embedding.weight

        # Initialize weights
        self.apply(self._init_weights)

        # Scale embeddings for stability
        with torch.no_grad():
            self.embeddings.token_embedding.weight.mul_(0.01)
            self.embeddings.position_embedding.weight.mul_(0.01)

        print(f"Advanced GPT-2 initialized: vocab_size={vocab_size}, embed_dim={embed_dim}, "
              f"layers={num_layers}, diffusion_blocks={num_diffusion_blocks}")

    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            # Scaled initialization based on layer depth
            std = 0.02
            if hasattr(module, 'weight'):
                torch.nn.init.normal_(module.weight, mean=0.0, std=std)
            if hasattr(module, 'bias') and module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            if hasattr(module, 'bias'):
                torch.nn.init.zeros_(module.bias)
            if hasattr(module, 'weight'):
                torch.nn.init.ones_(module.weight)

    def forward(self, input_ids: torch.Tensor, targets: Optional[torch.Tensor] = None,
                task_ids: Optional[torch.Tensor] = None, segment_ids: Optional[torch.Tensor] = None,
                use_diffusion_loss: bool = True):
        B, T = input_ids.shape
        device = input_ids.device

        # Advanced conditional embeddings
        x = self.embeddings(input_ids, task_ids, segment_ids)
        x = self.embed_dropout(x)

        # Create causal mask
        causal_mask = torch.triu(torch.ones(T, T, device=device), diagonal=1).bool()

        # Apply advanced transformer blocks
        diffusion_losses = []
        predicted_noises = []

        for i, block in enumerate(self.blocks):
            # Generate random timestep for diffusion blocks
            timestep = None
            if hasattr(block, 'use_diffusion') and block.use_diffusion:
                timestep = torch.randint(0, 1000, (1,)).item()

            x, predicted_noise = block(x, timestep, causal_mask)

            # Collect diffusion information
            if predicted_noise is not None:
                predicted_noises.append(predicted_noise)

        # Final layer norm
        x = self.ln_f(x)

        # Output projection
        logits = self.lm_head(x)

        # Compute losses
        loss = None
        diffusion_loss = None

        if targets is not None:
            # Main language modeling loss with label smoothing
            lm_loss = F.cross_entropy(
                logits.view(-1, self.vocab_size),
                targets.view(-1),
                label_smoothing=0.1
            )

            # Diffusion loss (if using diffusion blocks)
            if use_diffusion_loss and predicted_noises:
                diffusion_loss = 0.0
                for predicted_noise in predicted_noises:
                    # Simple noise prediction loss
                    target_noise = torch.randn_like(predicted_noise) * 0.1
                    diffusion_loss += F.mse_loss(predicted_noise, target_noise)
                diffusion_loss /= len(predicted_noises)

                # Combine losses with weighting
                loss = lm_loss + 0.1 * diffusion_loss
            else:
                loss = lm_loss

        return logits, loss

# =============================================================================
# COSINE LEARNING RATE SCHEDULER
# =============================================================================

def get_cosine_schedule_with_warmup(optimizer, num_warmup_steps, num_training_steps, min_lr_ratio=0.1):
    """Cosine learning rate schedule with warmup"""
    def lr_lambda(current_step):
        if current_step < num_warmup_steps:
            return float(current_step) / float(max(1, num_warmup_steps))
        progress = float(current_step - num_warmup_steps) / float(max(1, num_training_steps - num_warmup_steps))
        return max(min_lr_ratio, 0.5 * (1.0 + math.cos(math.pi * progress)))

    return torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)

# =============================================================================
# MAIN TRAINING FUNCTION
# =============================================================================

def main():
    parser = argparse.ArgumentParser(description="Improved GPT-2 Training")

    # Data configuration
    parser.add_argument("--data_dir", type=str, default=str(DATA_DIR))
    parser.add_argument("--download_data", action="store_true")
    parser.add_argument("--num_chunks", type=int, default=10)

    # Model configuration
    parser.add_argument("--model_size", choices=["small", "medium", "large"], default="medium")

    # Advanced features
    parser.add_argument("--use_diffusion", action="store_true", default=True, help="Use diffusion blocks")
    parser.add_argument("--diffusion_ratio", type=float, default=0.5, help="Ratio of diffusion blocks")
    parser.add_argument("--num_tasks", type=int, default=8, help="Number of task embeddings")
    parser.add_argument("--num_segments", type=int, default=4, help="Number of segment embeddings")
    parser.add_argument("--sparsity_ratio", type=float, default=0.1, help="Attention sparsity ratio")

    # Training configuration
    parser.add_argument("--seq_len", type=int, default=512)
    parser.add_argument("--lr", type=float, default=6e-4)
    parser.add_argument("--min_lr", type=float, default=6e-5)
    parser.add_argument("--batch", type=int, default=8)
    parser.add_argument("--grad_accum", type=int, default=8)
    parser.add_argument("--iters", type=int, default=10000)
    parser.add_argument("--warmup_iters", type=int, default=500)
    parser.add_argument("--eval_interval", type=int, default=500)
    parser.add_argument("--eval_iters", type=int, default=50)
    parser.add_argument("--log_interval", type=int, default=50)

    # Optimization
    parser.add_argument("--weight_decay", type=float, default=0.1)
    parser.add_argument("--grad_clip", type=float, default=1.0)

    # System
    parser.add_argument("--seed", type=int, default=42)
    parser.add_argument("--compile", action="store_true", help="Use torch.compile")

    # Parse arguments
    if "COLAB_GPU" in os.environ or "VSCODE_NOTEBOOK_CELL_LIST" in os.environ:
        args = parser.parse_args([])
        print("🔧 Colab detected: using default settings")
    else:
        args = parser.parse_args()

    # Download data if needed
    if args.download_data or not Path(args.data_dir).exists():
        download_fineweb_data(num_chunks=args.num_chunks)

    # Set seed
    set_seed(args.seed)

    # Model configurations - proper sizes
    model_configs = {
        "small": dict(layers=6, heads=6, embed=384),      # ~20M params
        "medium": dict(layers=8, heads=8, embed=512),     # ~50M params
        "large": dict(layers=12, heads=12, embed=768),    # ~125M params
    }

    config = model_configs[args.model_size]

    # Initialize accelerator
    accelerator = Accelerator(
        mixed_precision="fp16" if torch.cuda.is_available() else "no",
        gradient_accumulation_steps=args.grad_accum
    )

    # Datasets
    vocab_size = 50257

    print("Loading datasets...")
    train_ds = ImprovedBinDataset(args.data_dir, "train", args.seq_len, vocab_size)
    val_ds = ImprovedBinDataset(args.data_dir, "val", args.seq_len, vocab_size)

    # Data loaders
    train_loader = DataLoader(
        train_ds,
        batch_size=args.batch,
        shuffle=True,
        num_workers=2 if not ("COLAB_GPU" in os.environ) else 0,
        pin_memory=torch.cuda.is_available(),
        drop_last=True
    )

    val_loader = DataLoader(
        val_ds,
        batch_size=args.batch,
        shuffle=False,
        num_workers=2 if not ("COLAB_GPU" in os.environ) else 0,
        pin_memory=torch.cuda.is_available(),
        drop_last=True
    )

    # Model with advanced features
    model = ImprovedGPT2(
        vocab_size=vocab_size,
        embed_dim=config["embed"],
        num_layers=config["layers"],
        num_heads=config["heads"],
        max_length=args.seq_len,
        dropout=0.1,
        num_tasks=args.num_tasks,
        num_segments=args.num_segments,
        use_diffusion=args.use_diffusion,
        diffusion_ratio=args.diffusion_ratio
    )

    # Compile model if requested
    if args.compile and hasattr(torch, 'compile'):
        print("Compiling model...")
        model = torch.compile(model)

    total_params = sum(p.numel() for p in model.parameters())
    print(f"Model parameters: {total_params:,}")

    # Optimizer with weight decay
    def configure_optimizers(model, weight_decay, learning_rate):
        decay_params = []
        no_decay_params = []

        for name, param in model.named_parameters():
            if param.requires_grad:
                if 'bias' in name or 'ln' in name or 'norm' in name:
                    no_decay_params.append(param)
                else:
                    decay_params.append(param)

        return torch.optim.AdamW([
            {'params': decay_params, 'weight_decay': weight_decay},
            {'params': no_decay_params, 'weight_decay': 0.0}
        ], lr=learning_rate, betas=(0.9, 0.95), eps=1e-8)

    optimizer = configure_optimizers(model, args.weight_decay, args.lr)

    # Learning rate scheduler
    scheduler = get_cosine_schedule_with_warmup(
        optimizer,
        num_warmup_steps=args.warmup_iters,
        num_training_steps=args.iters,
        min_lr_ratio=args.min_lr / args.lr
    )

    # Prepare with accelerator
    model, optimizer, train_loader, val_loader, scheduler = accelerator.prepare(
        model, optimizer, train_loader, val_loader, scheduler
    )

    # Training loop
    print("\n🚀 Starting training...")
    model.train()

    train_losses = []
    val_losses = []
    best_val_loss = float('inf')

    start_time = time.time()

    for step in range(args.iters):
        # Training step
        for micro_step in range(args.grad_accum):
            try:
                batch = next(iter(train_loader))
                x, y = batch

                with accelerator.accumulate(model):
                    logits, loss = model(x, y)
                    accelerator.backward(loss)

                    if accelerator.sync_gradients:
                        # Gradient clipping
                        accelerator.clip_grad_norm_(model.parameters(), args.grad_clip)

                    optimizer.step()
                    scheduler.step()
                    optimizer.zero_grad()

                train_losses.append(loss.item())

            except StopIteration:
                # Restart data loader
                train_loader = DataLoader(
                    train_ds,
                    batch_size=args.batch,
                    shuffle=True,
                    num_workers=2 if not ("COLAB_GPU" in os.environ) else 0,
                    pin_memory=torch.cuda.is_available(),
                    drop_last=True
                )
                train_loader = accelerator.prepare(train_loader)

        # Logging
        if (step + 1) % args.log_interval == 0:
            avg_loss = np.mean(train_losses[-args.log_interval:])
            elapsed = time.time() - start_time
            current_lr = scheduler.get_last_lr()[0]
            print(f"Step {step+1:>5}/{args.iters} | Loss {avg_loss:.4f} | LR {current_lr:.2e} | Time {elapsed:.1f}s")

        # Evaluation
        if (step + 1) % args.eval_interval == 0:
            model.eval()
            eval_losses = []

            with torch.no_grad():
                for eval_step, batch in enumerate(val_loader):
                    if eval_step >= args.eval_iters:
                        break
                    x, y = batch
                    logits, loss = model(x, y)
                    eval_losses.append(loss.item())

            val_loss = np.mean(eval_losses)
            val_losses.append(val_loss)

            print(f"\n📊 Validation at step {step+1}: Loss = {val_loss:.4f}")

            # Save best model
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                if accelerator.is_main_process:
                    print(f"💾 New best model! Validation loss: {val_loss:.4f}")
                    # Save checkpoint
                    checkpoint = {
                        'model_state_dict': accelerator.unwrap_model(model).state_dict(),
                        'optimizer_state_dict': optimizer.state_dict(),
                        'scheduler_state_dict': scheduler.state_dict(),
                        'step': step + 1,
                        'val_loss': val_loss,
                        'config': config,
                        'args': vars(args)
                    }
                    torch.save(checkpoint, 'best_model.pt')

            model.train()

            # Early stopping check
            if val_loss < 4.0:
                print(f"\n🎯 Target validation loss reached: {val_loss:.4f}")
                break

        # Clear cache periodically
        if (step + 1) % 1000 == 0 and torch.cuda.is_available():
            torch.cuda.empty_cache()

    print(f"\n🎬 Training completed! Best validation loss: {best_val_loss:.4f}")

if __name__ == "__main__":
    main()