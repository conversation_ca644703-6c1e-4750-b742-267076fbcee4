# ... (all the code before main() remains the same) ...

# -----------------------------------------------------------------------------
# Main training loop (Accelerate)
# -----------------------------------------------------------------------------

def main():
    # Set CUDA_LAUNCH_BLOCKING=1 for debugging device-side errors
    # This is already in the original code, good for debugging.
    # os.environ['CUDA_LAUNCH_BLOCKING'] = '1' # Keep this if debugging CUDA issues
    # os.environ['TORCH_USE_CUDA_DSA'] = '1' # Enable for device-side assertions with PyTorch 2.0+


    parser = argparse.ArgumentParser()
    # Data paths
    parser.add_argument("--train_bin", type=str, default=str(LOCAL_DATA_DIR / "fineweb_train_*.bin"))
    parser.add_argument("--val_bin", type=str, default=str(LOCAL_DATA_DIR / "fineweb_val_*.bin"))
    # Model sizes
    parser.add_argument("--model_size", choices=["d12", "d24", "d36", "d48"], default="d12")
    # Optimisation
    parser.add_argument("--lr", type=float, default=1e-4)
    parser.add_argument("--batch", type=int, default=4) # Consider reducing if OOM on Colab
    parser.add_argument("--seq", type=int, default=512) # Max sequence length
    parser.add_argument("--grad_accum", type=int, default=1)
    parser.add_argument("--iters", type=int, default=5000)
    parser.add_argument("--weight_decay", type=float, default=0.1)
    parser.add_argument("--damping", type=float, default=1e-4) # Set to 0 to disable damping loss
    # ----> ADDED GRAD_CLIP ARGUMENT HERE <----
    parser.add_argument("--grad_clip", type=float, default=1.0, help="Gradient clipping value (0.0 or negative to disable)")
    # Misc
    parser.add_argument("--bf16", action="store_true", help="use bfloat16 precision")
    parser.add_argument("--wandb", action="store_true")
    parser.add_argument("--output", type=str, default="runs")

    # For Colab/Jupyter: if running interactively, parse_args might need an empty list.
    # If running as script, it will parse from sys.argv
    if "COLAB_GPU" in os.environ or "VSCODE_NOTEBOOK_CELL_LIST" in os.environ or "IPYKERNEL_LAUNCH_TIMEOUT" in os.environ :
        args = parser.parse_args([])
    else:
        args = parser.parse_args()


    size2cfg = {
        "d12": dict(layers=12, heads=12, embed=768, max_length=1024), # Added max_length for clarity
        "d24": dict(layers=24, heads=16, embed=1024, max_length=1024),
        "d36": dict(layers=36, heads=20, embed=1280, max_length=1024),
        "d48": dict(layers=48, heads=25, embed=1600, max_length=1024),
    }[args.model_size]

    # Ensure sequence length for training does not exceed model's capacity
    if args.seq > size2cfg["max_length"]:
        print(f"Warning: Training sequence length {args.seq} is greater than model's max_length {size2cfg['max_length']}. Clamping to {size2cfg['max_length']}.")
        args.seq = size2cfg["max_length"]


    # Accelerator handles DDP and mixed precision for us
    ddp_kwargs = DistributedDataParallelKwargs(find_unused_parameters=False) # Set True if some outputs not used in loss
    # For gradient_as_bucket_view=True if using PyTorch >= 1.12 with DDP (can improve perf)
    # ddp_kwargs = DistributedDataParallelKwargs(find_unused_parameters=False, gradient_as_bucket_view=True)

    accelerator = Accelerator(
        mixed_precision="bf16" if args.bf16 and torch.cuda.is_bf16_supported() else ("fp16" if args.bf16 else "no"),
        kwargs_handlers=[ddp_kwargs],
        gradient_accumulation_steps=args.grad_accum
    )
    device = accelerator.device
    
    if args.bf16 and not torch.cuda.is_bf16_supported() and accelerator.mixed_precision == "bf16":
        accelerator.print("Warning: BF16 selected but not supported on this hardware. Mixed precision might not work as expected or fallback.")


    # Data
    vocab_size = 50257 # Standard GPT-2 vocab size
    train_ds = BinDataset(args.train_bin, args.seq, vocab_size=vocab_size)
    val_ds = BinDataset(args.val_bin, args.seq, vocab_size=vocab_size)

    # For streaming datasets, num_workers=0 is often recommended to avoid complex worker state.
    # pin_memory=True can speed up CPU to GPU transfers IF num_workers > 0. With num_workers=0, it has less impact.
    train_loader = DataLoader(train_ds, batch_size=args.batch, pin_memory=(accelerator.device.type == 'cuda'), num_workers=0)
    val_loader = DataLoader(val_ds, batch_size=args.batch, pin_memory=(accelerator.device.type == 'cuda'), num_workers=0)


    # Model, loss, optimiser
    model = GPT2Diffusion(
        vocab_size=vocab_size,
        embed_dim=size2cfg["embed"],
        num_layers=size2cfg["layers"],
        num_heads=size2cfg["heads"],
        max_length=size2cfg["max_length"] # Pass max_length to model
    )
    ce_loss = nn.CrossEntropyLoss() # Default ignore_index is -100

    # Initialize StructuralDampingLoss. Model is on CPU at this point.
    damping_loss_module = StructuralDampingLoss(model, coeff=args.damping)

    optim = torch.optim.AdamW(model.parameters(), lr=args.lr, weight_decay=args.weight_decay, betas=(0.9, 0.95), eps=1e-8)

    # Prepare everything for distributed training
    model, optim, train_loader, val_loader, damping_loss_module = accelerator.prepare(
        model, optim, train_loader, val_loader, damping_loss_module
    )
    # After prepare, model and damping_loss_module (and its buffers) are on the correct device.

    if accelerator.is_main_process and args.wandb:
        try:
            import wandb
            run_name = f"{args.model_size}-seq{args.seq}-bs{args.batch*args.grad_accum*accelerator.num_processes}-lr{args.lr}"
            if args.bf16: run_name += "-bf16"
            wandb.init(project="OpenCAP", config=vars(args), name=run_name)
        except ModuleNotFoundError:
            print("Warning: wandb not found or import failed. Skipping wandb logging.")
            args.wandb = False
            wandb = None
        except Exception as e:
            print(f"Warning: wandb.init failed: {e}. Skipping wandb logging.")
            args.wandb = False
            wandb = None


    def evaluate() -> float:
        model.eval()
        losses = []
        val_iter = iter(val_loader) # Get iterator from prepared DataLoader
        with torch.no_grad():
            for eval_step in range(100):  # limit to ~100 batches for speed
                try:
                    x, y = next(val_iter)
                    # Data from prepared DataLoader should be on the correct device
                except StopIteration:
                    if eval_step == 0: # No data in val_loader
                        accelerator.print("Warning: Validation dataset is empty or exhausted immediately.")
                        return float('inf')
                    accelerator.print("End of validation dataset reached early.")
                    break

                logits = model(x) # x is (B, T)
                # y is (B, T)
                # CrossEntropyLoss expects logits (B*T, C) and targets (B*T)
                loss = ce_loss(logits.view(-1, logits.size(-1)), y.view(-1))
                # Gather losses from all processes for accurate mean
                # gather_for_metrics expects a tensor, not a list of tensors
                gathered_losses = accelerator.gather_for_metrics(loss.reshape(1)) # Reshape to be a 1D tensor
                losses.append(gathered_losses)

        model.train()
        if not losses: # No batches processed
            return float('inf')

        # Concatenate all gathered losses and calculate mean
        all_losses_tensor = torch.cat(losses)
        return all_losses_tensor.mean().item()


    # ---------------------------------------------------------------------
    # Training loop
    # ---------------------------------------------------------------------
    model.train()
    # total_tokens_processed should be a global counter, careful with DDP
    # For logging, usually step * batch_size * seq_len * num_processes * grad_accum
    # Simpler: just use `step`.
    
    train_iter = iter(train_loader) # Get iterator from prepared DataLoader

    for step in range(args.iters):
        # Gradient accumulation loop is handled by `accelerator.accumulate`
        with accelerator.accumulate(model):
            try:
                x, y = next(train_iter)
            except StopIteration:
                accelerator.print(f"End of training dataset reached at step {step}. Re-initializing train_loader.")
                # When re-initializing, it's good practice to re-prepare if the loader has internal state
                # that might be affected by distributed setup, though for simple iterables it might not be strictly necessary.
                # DataLoader itself is wrapped, so re-preparing the original train_ds and then making a new DataLoader
                # might be more robust if there were complex worker states.
                # For now, re-preparing the existing train_loader object.
                train_loader_prepared = accelerator.prepare(train_loader) # In case internal Accelerate state needs reset
                train_iter = iter(train_loader_prepared)

                try:
                    x,y = next(train_iter)
                except StopIteration:
                    accelerator.print(f"CRITICAL: train_loader exhausted immediately after re-initialization at step {step}. Stopping.")
                    break # Exit outer loop

            # accelerator.autocast() is implicitly handled by accelerator.prepare and mixed_precision setting
            logits = model(x)
            loss_main = ce_loss(logits.view(-1, logits.size(-1)), y.view(-1))

            # Get unwrapped model for damping loss if DDP is used
            unwrapped_model = accelerator.unwrap_model(model) # This is safe even if not DDP
            loss_reg = damping_loss_module(unwrapped_model)

            loss = loss_main + loss_reg

            accelerator.backward(loss)

            # Operations to perform once gradients are all accumulated and synchronized
            if accelerator.sync_gradients:
                if args.grad_clip > 0: # Check if positive, as 0.0 or negative means no clipping
                    accelerator.clip_grad_norm_(model.parameters(), args.grad_clip)

            optim.step() # Optimizer step is inside accumulate but only runs when needed
            optim.zero_grad(set_to_none=True) # Zero grad is also inside accumulate

        # Logging - only after all accumulation steps (i.e., when sync_gradients would be true for the *last* microbatch of an accumulation cycle)
        # More accurately, log when an optimizer step has occurred.
        # accelerator.sync_gradients is True on the step when gradients are actually synchronized and optimizer updates.
        if accelerator.sync_gradients:
            if accelerator.is_main_process:
                # Calculate effective batch size for logging
                eff_batch_size = args.batch * args.grad_accum * accelerator.num_processes
                
                if step % 50 == 0: # Log every 50 optimizer steps
                    ppl = math.exp(loss_main.item()) # Perplexity from main loss of the last micro-batch
                    accelerator.print(f"Iter {step:>6}/{args.iters} | Train Loss {loss_main.item():.4f} | Reg Loss {loss_reg.item():.4f} | PPL {ppl:.2f} | Eff Batch {eff_batch_size}")
                    if args.wandb and wandb:
                        wandb.log({
                            "train/loss_main": loss_main.item(), # Loss of last microbatch
                            "train/loss_reg": loss_reg.item(),   # Loss of last microbatch
                            "train/loss_total": loss.item(),     # Loss of last microbatch
                            "train/ppl": ppl,
                            "progress/step": step, # This is the micro-batch step
                            "progress/optimizer_step": step // args.grad_accum, # More accurate optimizer step
                            "params/lr": optim.param_groups[0]['lr'] # Log learning rate
                        })

                if step > 0 and step % 200 == 0: # Evaluate every 200 optimizer steps
                    val_loss = evaluate()
                    accelerator.print(f"Iter {step:>6}/{args.iters} | Val Loss {val_loss:.4f}")
                    if args.wandb and wandb:
                        wandb.log({
                            "val/loss": val_loss,
                            "progress/step": step, # Micro-batch step
                            "progress/optimizer_step": step // args.grad_accum,
                        })
        
        if step >= args.iters -1 : # Ensure loop terminates correctly after args.iters steps
            accelerator.print(f"Reached target iterations: {args.iters}. Stopping training.")
            break


    # Save final checkpoint
    if accelerator.is_main_process:
        output_dir = Path(args.output)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        checkpoint_name = f"final_checkpoint_{args.model_size}_iters{args.iters}"
        # Using accelerator.save_state for comprehensive checkpointing (model, optimizer, rng states etc.)
        accelerator.save_state(str(output_dir / checkpoint_name))
        accelerator.print(f"Final accelerator state saved to {output_dir / checkpoint_name}")

        # Optionally, also save just the unwrapped model's state_dict
        unwrapped_model = accelerator.unwrap_model(model)
        model_save_path = output_dir / f"model_final_{args.model_size}_iters{args.iters}.pt"
        torch.save(unwrapped_model.state_dict(), model_save_path)
        accelerator.print(f"Final unwrapped model state_dict saved to {model_save_path}")


        if args.wandb and wandb:
            # wandb.save(str(model_save_path)) # Save the .pt model file
            # Or save the whole checkpoint directory if preferred and wandb supports it well
            # For directories, it's often better to tar.gz it first or use wandb artifacts
            wandb.save(str(model_save_path))
            # Example for saving checkpoint directory files (might be many if not careful)
            # for fpath in (output_dir / checkpoint_name).glob("**/*"):
            #     if fpath.is_file():
            #         wandb.save(str(fpath), base_path=str(output_dir/checkpoint_name))


    if args.wandb and wandb and accelerator.is_main_process:
        wandb.finish()


if __name__ == "__main__":
    main()
