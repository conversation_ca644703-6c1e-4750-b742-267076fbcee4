import os
import sys
import math
import argparse
import numpy as np
from pathlib import Path
from typing import Optional, Union, Tuple, Dict

# Set CUDA debugging environment variables BEFORE importing torch
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ['TORCH_USE_CUDA_DSA'] = '1'

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset

# Clear any existing CUDA cache before starting
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    torch.cuda.synchronize()

from accelerate import Accelerator, DistributedDataParallelKwargs
from accelerate.utils import set_seed
from huggingface_hub import hf_hub_download

# Data directory setup
SCRIPT_DIR = Path(__file__).parent if "__file__" in globals() else Path(".")
DATA_DIR = SCRIPT_DIR / "fineweb10B"

def download_fineweb_data(num_chunks: int = 3):
    """Download Fineweb10B dataset from HuggingFace - REDUCED DEFAULT"""
    print("Downloading Fineweb10B dataset...")
    
    def get(fname):
        local_dir = str(DATA_DIR)
        os.makedirs(local_dir, exist_ok=True)
        if not os.path.exists(os.path.join(local_dir, fname)):
            print(f"Downloading {fname}...")
            hf_hub_download(
                repo_id="kjj0/fineweb10B-gpt2",
                filename=fname,
                repo_type="dataset",
                local_dir=local_dir,
            )
        else:
            print(f"{fname} already exists, skipping download")
    
    # Download validation data
    get("fineweb_val_%06d.bin" % 0)
    
    # Download training data chunks - REDUCED NUMBER
    for i in range(1, min(num_chunks + 1, 4)):  # Max 3 chunks to save memory
        get("fineweb_train_%06d.bin" % i)
    
    print(f"Dataset download complete! Files saved to {DATA_DIR}")

# =============================================================================
# SIMPLE DATASET FOR DEBUGGING
# =============================================================================

class DebugBinDataset(Dataset):
    """Simplified dataset for debugging CUDA issues"""
    def __init__(self, data_dir: str, split: str, seq_len: int, vocab_size: int = 50257):
        super().__init__()
        self.seq_len = seq_len
        self.vocab_size = vocab_size
        self.data_dir = Path(data_dir)
        
        # Find binary files
        if split == "train":
            pattern = "fineweb_train_*.bin"
        elif split == "val":
            pattern = "fineweb_val_*.bin"
        else:
            raise ValueError(f"Unknown split: {split}")
        
        self.bin_files = sorted(self.data_dir.glob(pattern))
        if not self.bin_files:
            print(f"Warning: No files found: {self.data_dir}/{pattern}")
            print("Creating synthetic data for debugging...")
            self.use_synthetic = True
            self.total_samples = 1000
        else:
            self.use_synthetic = False
            self.total_samples = min(1000, len(self.bin_files) * 100)  # Limited samples
            print(f"Found {len(self.bin_files)} files for {split}")
        
        print(f"Total {split} samples: {self.total_samples}")
        
        # Try to load first file to check data
        if not self.use_synthetic and self.bin_files:
            try:
                print(f"Checking first file: {self.bin_files[0]}")
                test_data = np.memmap(str(self.bin_files[0]), dtype=np.uint16, mode='r')
                print(f"File size: {len(test_data)} tokens")
                print(f"Min token: {test_data[:1000].min()}, Max token: {test_data[:1000].max()}")
                
                # Check if tokens are out of range
                if test_data[:1000].max() >= vocab_size:
                    print(f"WARNING: Found tokens >= vocab_size ({vocab_size})")
                    print("Will clamp tokens to valid range")
                    
            except Exception as e:
                print(f"Error checking file: {e}")
                self.use_synthetic = True
    
    def __len__(self):
        return self.total_samples
    
    def __getitem__(self, idx):
        if self.use_synthetic:
            # Generate synthetic data for debugging
            x = torch.randint(0, min(1000, self.vocab_size), (self.seq_len,), dtype=torch.long)
            y = torch.randint(0, min(1000, self.vocab_size), (self.seq_len,), dtype=torch.long)
            return x, y
        
        # Simplified file reading
        file_idx = idx % len(self.bin_files)
        
        try:
            # Load file data
            file_data = np.memmap(str(self.bin_files[file_idx]), dtype=np.uint16, mode='r')
            
            # Get a random offset
            max_start = max(0, len(file_data) - self.seq_len - 1)
            if max_start == 0:
                offset = 0
            else:
                offset = (idx * 1234567) % max_start  # Deterministic offset
            
            # Extract sequence
            x_data = file_data[offset:offset + self.seq_len].astype(np.int64)
            y_data = file_data[offset + 1:offset + self.seq_len + 1].astype(np.int64)
            
            # Convert to tensor and clamp
            x = torch.from_numpy(x_data)
            y = torch.from_numpy(y_data)
            
            # CRITICAL: Ensure all tokens are valid
            x = torch.clamp(x, min=0, max=self.vocab_size - 1)
            y = torch.clamp(y, min=0, max=self.vocab_size - 1)
            
            # Pad if necessary
            if len(x) < self.seq_len:
                pad_len = self.seq_len - len(x)
                x = torch.cat([x, torch.zeros(pad_len, dtype=torch.long)])
                y = torch.cat([y, torch.zeros(pad_len, dtype=torch.long)])
            
            return x, y
            
        except Exception as e:
            print(f"Error reading file {self.bin_files[file_idx]}: {e}")
            # Return synthetic data on error
            x = torch.randint(0, min(1000, self.vocab_size), (self.seq_len,), dtype=torch.long)
            y = torch.randint(0, min(1000, self.vocab_size), (self.seq_len,), dtype=torch.long)
            return x, y

# =============================================================================
# SIMPLE MODEL FOR DEBUGGING
# =============================================================================

class SimpleGPT2(nn.Module):
    """Simplified GPT-2 for debugging"""
    def __init__(self, vocab_size: int, embed_dim: int, num_layers: int, 
                 num_heads: int, max_length: int, dropout: float = 0.1):
        super().__init__()
        self.vocab_size = vocab_size
        self.embed_dim = embed_dim
        self.max_length = max_length
        
        # Embeddings
        self.token_embedding = nn.Embedding(vocab_size, embed_dim)
        self.position_embedding = nn.Embedding(max_length, embed_dim)
        self.dropout = nn.Dropout(dropout)
        
        # Transformer blocks
        self.blocks = nn.ModuleList([
            nn.TransformerEncoderLayer(
                d_model=embed_dim,
                nhead=num_heads,
                dim_feedforward=4 * embed_dim,
                dropout=dropout,
                batch_first=True
            ) for _ in range(num_layers)
        ])
        
        # Output
        self.ln_f = nn.LayerNorm(embed_dim)
        self.lm_head = nn.Linear(embed_dim, vocab_size, bias=False)
        
        # Tie weights
        self.lm_head.weight = self.token_embedding.weight
        
        # Initialize weights
        self.apply(self._init_weights)
        
        print(f"Model initialized: vocab_size={vocab_size}, embed_dim={embed_dim}, layers={num_layers}")
        
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
    
    def forward(self, input_ids: torch.Tensor, targets: Optional[torch.Tensor] = None):
        B, T = input_ids.shape
        device = input_ids.device
        
        # Debug info
        if torch.cuda.is_available():
            print(f"Forward pass: B={B}, T={T}, device={device}")
            print(f"Input range: [{input_ids.min().item()}, {input_ids.max().item()}]")
        
        # SAFETY CHECK
        if input_ids.max() >= self.vocab_size:
            print(f"ERROR: Token {input_ids.max().item()} >= vocab_size {self.vocab_size}")
            input_ids = torch.clamp(input_ids, min=0, max=self.vocab_size - 1)
        
        if input_ids.min() < 0:
            print(f"ERROR: Negative token id: {input_ids.min().item()}")
            input_ids = torch.clamp(input_ids, min=0, max=self.vocab_size - 1)
        
        # Embeddings
        try:
            token_emb = self.token_embedding(input_ids)
        except Exception as e:
            print(f"Token embedding error: {e}")
            print(f"Input shape: {input_ids.shape}, dtype: {input_ids.dtype}")
            print(f"Vocab size: {self.vocab_size}")
            raise
            
        pos_ids = torch.arange(T, device=device).unsqueeze(0).expand(B, -1)
        pos_emb = self.position_embedding(pos_ids)
        
        x = self.dropout(token_emb + pos_emb)
        
        # Apply transformer blocks
        for i, block in enumerate(self.blocks):
            # Create causal mask
            mask = torch.triu(torch.ones(T, T, device=device), diagonal=1).bool()
            x = block(x, src_mask=mask, is_causal=True)
        
        # Final layer norm
        x = self.ln_f(x)
        
        # Output projection
        logits = self.lm_head(x)
        
        if targets is not None:
            # Ensure targets are valid
            targets = torch.clamp(targets, min=0, max=self.vocab_size - 1)
            
            # Compute loss
            loss = F.cross_entropy(
                logits.view(-1, self.vocab_size),
                targets.view(-1),
                ignore_index=-100
            )
            
            return loss, {'loss': loss.item()}
        
        return logits

# =============================================================================
# MAIN TRAINING FUNCTION - SIMPLIFIED FOR DEBUGGING
# =============================================================================

def main():
    parser = argparse.ArgumentParser(description="Debug OpenCAP Training")
    
    # Data configuration
    parser.add_argument("--data_dir", type=str, default=str(DATA_DIR))
    parser.add_argument("--download_data", action="store_true")
    
    # Model configuration
    parser.add_argument("--model_size", choices=["nano", "tiny"], default="nano")
    
    # Training configuration
    parser.add_argument("--seq_len", type=int, default=64)
    parser.add_argument("--lr", type=float, default=3e-4)
    parser.add_argument("--batch", type=int, default=1)
    parser.add_argument("--grad_accum", type=int, default=8)
    parser.add_argument("--iters", type=int, default=100)
    
    # System
    parser.add_argument("--seed", type=int, default=42)
    
    # Parse arguments
    if "COLAB_GPU" in os.environ or "VSCODE_NOTEBOOK_CELL_LIST" in os.environ:
        args = parser.parse_args([])
        print("🔧 Colab detected: using debug settings")
    else:
        args = parser.parse_args()
    
    # Download data if needed
    if args.download_data or not Path(args.data_dir).exists():
        download_fineweb_data(num_chunks=1)
    
    # Debug GPU info
    if torch.cuda.is_available():
        print(f"CUDA available: {torch.cuda.get_device_name()}")
        print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        
        # Try simple CUDA operations
        try:
            print("Testing basic CUDA operations...")
            test_tensor = torch.randn(10, 10).cuda()
            test_result = test_tensor @ test_tensor.T
            print("Basic CUDA operations: OK")
        except Exception as e:
            print(f"CUDA test failed: {e}")
            print("Falling back to CPU")
            os.environ['CUDA_VISIBLE_DEVICES'] = ''
    
    # Set seed AFTER CUDA tests
    try:
        set_seed(args.seed)
        print(f"Seed set to {args.seed}")
    except Exception as e:
        print(f"Warning: Could not set seed: {e}")
        # Manual seed setting
        np.random.seed(args.seed)
        torch.manual_seed(args.seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(args.seed)
    
    # Model configurations - ULTRA SMALL
    size2cfg = {
        "nano": dict(layers=2, heads=2, embed=128, max_length=128),     # ~1M params
        "tiny": dict(layers=3, heads=4, embed=192, max_length=256),     # ~3M params
    }[args.model_size]
    
    # Initialize accelerator with minimal config
    try:
        accelerator = Accelerator(
            mixed_precision="no",  # Disable mixed precision for debugging
            gradient_accumulation_steps=args.grad_accum
        )
        print("Accelerator initialized")
    except Exception as e:
        print(f"Accelerator error: {e}")
        print("Running without accelerator")
        accelerator = None
    
    # Datasets
    vocab_size = 50257
    
    try:
        print("Loading datasets...")
        train_ds = DebugBinDataset(args.data_dir, "train", args.seq_len, vocab_size)
        val_ds = DebugBinDataset(args.data_dir, "val", args.seq_len, vocab_size)
    except Exception as e:
        print(f"Dataset error: {e}")
        return
    
    # Data loaders - no workers, no pinned memory
    train_loader = DataLoader(
        train_ds, batch_size=args.batch, shuffle=True, 
        pin_memory=False, num_workers=0, drop_last=True
    )
    val_loader = DataLoader(
        val_ds, batch_size=args.batch, shuffle=False,
        pin_memory=False, num_workers=0, drop_last=True
    )
    
    # Model
    model = SimpleGPT2(
        vocab_size=vocab_size,
        embed_dim=size2cfg["embed"],
        num_layers=size2cfg["layers"],
        num_heads=size2cfg["heads"],
        max_length=size2cfg["max_length"],
        dropout=0.0
    )
    
    total_params = sum(p.numel() for p in model.parameters())
    print(f"Model parameters: {total_params:,}")
    
    # Optimizer
    optimizer = torch.optim.AdamW(model.parameters(), lr=args.lr)
    
    # Move to device
    if accelerator:
        model, optimizer, train_loader, val_loader = accelerator.prepare(
            model, optimizer, train_loader, val_loader
        )
    else:
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model = model.to(device)
    
    # Test forward pass
    print("\n🧪 Testing forward pass...")
    try:
        with torch.no_grad():
            test_batch = next(iter(train_loader))
            x, y = test_batch
            print(f"Test batch shape: x={x.shape}, y={y.shape}")
            print(f"Test batch range: x=[{x.min().item()}, {x.max().item()}]")
            
            if accelerator:
                loss, loss_dict = model(x, y)
            else:
                x, y = x.to(device), y.to(device)
                loss, loss_dict = model(x, y)
                
            print(f"Test forward pass successful! Loss: {loss.item():.4f}")
    except Exception as e:
        print(f"❌ Forward pass failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Training loop
    print("\n🚀 Starting training...")
    model.train()
    
    for step in range(args.iters):
        try:
            # Get batch
            batch = next(iter(train_loader))
            x, y = batch
            
            # Forward pass
            if accelerator:
                with accelerator.accumulate(model):
                    loss, loss_dict = model(x, y)
                    accelerator.backward(loss)
                    optimizer.step()
                    optimizer.zero_grad()
            else:
                x, y = x.to(device), y.to(device)
                loss, loss_dict = model(x, y)
                loss.backward()
                optimizer.step()
                optimizer.zero_grad()
            
            # Logging
            if step % 10 == 0:
                print(f"Step {step:>4}/{args.iters} | Loss {loss_dict['loss']:.4f}")
            
            # Clear cache periodically
            if step % 50 == 0 and torch.cuda.is_available():
                torch.cuda.empty_cache()
                
        except Exception as e:
            print(f"Training error at step {step}: {e}")
            if "out of memory" in str(e):
                print("Clearing CUDA cache...")
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                continue
            else:
                import traceback
                traceback.print_exc()
                break
    
    print("🎬 Training completed!")

if __name__ == "__main__":
    main()