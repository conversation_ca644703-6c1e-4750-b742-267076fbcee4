#!/usr/bin/env python3
"""
Advanced GPT-2 with Conditional Embeddings, Sparse Attention, and Diffusion Blocks

This implementation includes:
1. Advanced conditional embeddings (task, category, segment) inspired by DeepSeek/T5
2. Sparse attention with dynamic routing and relative position bias
3. Diffusion blocks with denoising capabilities
4. SwiGLU activation for efficient feed-forward networks
5. Pre-norm layer normalization for stability

Usage:
    python advanced_gpt2_improved.py --use_diffusion --diffusion_ratio 0.5 --sparsity_ratio 0.1
"""

import os
import sys
import math
import argparse
import numpy as np
from pathlib import Path
from typing import Optional, Union, Tuple, Dict
import time

# Set CUDA debugging environment variables BEFORE importing torch
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ['TORCH_USE_CUDA_DSA'] = '1'

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
from torch.nn.utils.rnn import pad_sequence

# Clear any existing CUDA cache before starting
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    torch.cuda.synchronize()

from accelerate import Accelerator, DistributedDataParallelKwargs
from accelerate.utils import set_seed
from huggingface_hub import hf_hub_download

# Data directory setup
SCRIPT_DIR = Path(__file__).parent if "__file__" in globals() else Path(".")
DATA_DIR = SCRIPT_DIR / "fineweb10B"

def download_fineweb_data(num_chunks: int = 10):
    """Download Fineweb10B dataset from HuggingFace - increased chunks for more data"""
    print("Downloading Fineweb10B dataset...")

    def get(fname):
        local_dir = str(DATA_DIR)
        os.makedirs(local_dir, exist_ok=True)
        if not os.path.exists(os.path.join(local_dir, fname)):
            print(f"Downloading {fname}...")
            hf_hub_download(
                repo_id="kjj0/fineweb10B-gpt2",
                filename=fname,
                repo_type="dataset",
                local_dir=local_dir,
            )
        else:
            print(f"{fname} already exists, skipping download")

    # Download validation data
    get("fineweb_val_%06d.bin" % 0)

    # Download training data chunks - increased for better training
    for i in range(1, min(num_chunks + 1, 11)):
        get("fineweb_train_%06d.bin" % i)

    print(f"Dataset download complete! Files saved to {DATA_DIR}")

# =============================================================================
# IMPROVED DATASET WITH PROPER SAMPLING
# =============================================================================

class ImprovedBinDataset(Dataset):
    """Improved dataset with better sampling and no token clamping issues"""
    def __init__(self, data_dir: str, split: str, seq_len: int, vocab_size: int = 50257):
        super().__init__()
        self.seq_len = seq_len
        self.vocab_size = vocab_size
        self.data_dir = Path(data_dir)

        # Find binary files
        if split == "train":
            pattern = "fineweb_train_*.bin"
        elif split == "val":
            pattern = "fineweb_val_*.bin"
        else:
            raise ValueError(f"Unknown split: {split}")

        self.bin_files = sorted(self.data_dir.glob(pattern))
        if not self.bin_files:
            raise ValueError(f"No files found: {self.data_dir}/{pattern}")

        print(f"Found {len(self.bin_files)} files for {split}")

        # Calculate total tokens available
        self.file_sizes = []
        self.cumulative_sizes = [0]
        total_tokens = 0

        for file_path in self.bin_files:
            file_size = os.path.getsize(file_path) // 2  # uint16 = 2 bytes
            self.file_sizes.append(file_size)
            total_tokens += file_size
            self.cumulative_sizes.append(total_tokens)

        # Calculate number of complete sequences we can extract
        self.total_sequences = total_tokens // (seq_len + 1)
        print(f"Total tokens: {total_tokens:,}, Total sequences: {self.total_sequences:,}")

    def __len__(self):
        return self.total_sequences

    def __getitem__(self, idx):
        # Calculate which file contains this sequence
        seq_start = idx * (self.seq_len + 1)

        # Binary search to find the right file
        file_idx = 0
        for i, cum_size in enumerate(self.cumulative_sizes[1:]):
            if seq_start < cum_size:
                file_idx = i
                break

        # Load the memory map
        file_path = self.bin_files[file_idx]
        data = np.memmap(str(file_path), dtype=np.uint16, mode='r')

        # Calculate offset within this file
        offset_in_file = seq_start - self.cumulative_sizes[file_idx]

        # Handle edge case where sequence spans multiple files
        if offset_in_file + self.seq_len + 1 > len(data):
            # Use a different random position within the same file
            offset_in_file = np.random.randint(0, len(data) - self.seq_len - 1)

        # Extract sequence
        chunk = data[offset_in_file:offset_in_file + self.seq_len + 1].astype(np.int64)

        # Clamp tokens to valid range (handle the vocab_size issue)
        chunk = np.clip(chunk, 0, self.vocab_size - 1)

        x = torch.from_numpy(chunk[:-1]).long()
        y = torch.from_numpy(chunk[1:]).long()

        return x, y

# =============================================================================
# ADVANCED COMPONENTS FOR IMPROVED ARCHITECTURE
# =============================================================================

class ConditionalEmbedding(nn.Module):
    """Advanced conditional embeddings inspired by DeepSeek and T5"""
    def __init__(self, vocab_size: int, embed_dim: int, max_length: int,
                 num_tasks: int = 8, num_segments: int = 4):
        super().__init__()
        self.embed_dim = embed_dim

        # Core embeddings
        self.token_embedding = nn.Embedding(vocab_size, embed_dim)
        self.position_embedding = nn.Embedding(max_length, embed_dim)

        # Conditional embeddings
        self.task_embedding = nn.Embedding(num_tasks, embed_dim)
        self.segment_embedding = nn.Embedding(num_segments, embed_dim)

        # Pre-norm layer normalization for stability
        self.embed_ln = nn.LayerNorm(embed_dim)

        # Learnable mixing weights
        self.mixing_weights = nn.Parameter(torch.ones(4) / 4)  # token, pos, task, segment

    def forward(self, input_ids: torch.Tensor, task_ids: Optional[torch.Tensor] = None,
                segment_ids: Optional[torch.Tensor] = None):
        B, T = input_ids.shape
        device = input_ids.device

        # Token embeddings
        token_emb = self.token_embedding(input_ids)

        # Position embeddings
        pos_ids = torch.arange(T, device=device, dtype=torch.long).unsqueeze(0).expand(B, -1)
        pos_emb = self.position_embedding(pos_ids)

        # Task embeddings (default to task 0 if not provided)
        if task_ids is None:
            task_ids = torch.zeros(B, device=device, dtype=torch.long)
        task_emb = self.task_embedding(task_ids).unsqueeze(1).expand(-1, T, -1)

        # Segment embeddings (default to segment 0 if not provided)
        if segment_ids is None:
            segment_ids = torch.zeros(B, T, device=device, dtype=torch.long)
        segment_emb = self.segment_embedding(segment_ids)

        # Weighted combination of embeddings
        weights = F.softmax(self.mixing_weights, dim=0)
        combined_emb = (weights[0] * token_emb +
                       weights[1] * pos_emb +
                       weights[2] * task_emb +
                       weights[3] * segment_emb)

        # Pre-norm for stability
        return self.embed_ln(combined_emb)


class RelativePositionBias(nn.Module):
    """Relative position bias inspired by T5 and DeBERTa"""
    def __init__(self, num_heads: int, max_distance: int = 128):
        super().__init__()
        self.num_heads = num_heads
        self.max_distance = max_distance

        # Learnable relative position embeddings
        self.relative_attention_bias = nn.Embedding(2 * max_distance + 1, num_heads)

    def forward(self, seq_len: int, device: torch.device):
        # Create relative position matrix
        positions = torch.arange(seq_len, device=device)
        relative_positions = positions.unsqueeze(0) - positions.unsqueeze(1)

        # Clamp to max distance
        relative_positions = torch.clamp(relative_positions, -self.max_distance, self.max_distance)
        relative_positions += self.max_distance  # Shift to positive indices

        # Get bias values
        bias = self.relative_attention_bias(relative_positions)  # [seq_len, seq_len, num_heads]
        return bias.permute(2, 0, 1)  # [num_heads, seq_len, seq_len]


class SparseAttention(nn.Module):
    """Sparse attention with dynamic routing and relative position bias"""
    def __init__(self, embed_dim: int, num_heads: int, dropout: float = 0.1,
                 sparsity_ratio: float = 0.1, max_distance: int = 128):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        self.sparsity_ratio = sparsity_ratio

        assert embed_dim % num_heads == 0, "embed_dim must be divisible by num_heads"

        # Linear projections
        self.q_proj = nn.Linear(embed_dim, embed_dim, bias=False)
        self.k_proj = nn.Linear(embed_dim, embed_dim, bias=False)
        self.v_proj = nn.Linear(embed_dim, embed_dim, bias=False)
        self.out_proj = nn.Linear(embed_dim, embed_dim)

        # Relative position bias
        self.relative_bias = RelativePositionBias(num_heads, max_distance)

        # Sparse routing network
        self.routing_net = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 4),
            nn.ReLU(),
            nn.Linear(embed_dim // 4, 1),
            nn.Sigmoid()
        )

        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor, attn_mask: Optional[torch.Tensor] = None):
        B, T, C = x.shape

        # Compute Q, K, V
        q = self.q_proj(x).view(B, T, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.k_proj(x).view(B, T, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.v_proj(x).view(B, T, self.num_heads, self.head_dim).transpose(1, 2)

        # Compute attention scores
        scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.head_dim)

        # Add relative position bias
        rel_bias = self.relative_bias(T, x.device)
        scores = scores + rel_bias.unsqueeze(0)  # [B, num_heads, T, T]

        # Dynamic sparse routing
        routing_scores = self.routing_net(x).squeeze(-1)  # [B, T]

        # Create sparse mask based on routing scores
        k_sparse = max(1, int(T * self.sparsity_ratio))
        _, top_indices = torch.topk(routing_scores, k=k_sparse, dim=-1)
        sparse_mask = torch.zeros_like(routing_scores, dtype=torch.bool)
        sparse_mask.scatter_(1, top_indices, True)

        # Apply sparse mask to attention (only mask keys, not queries)
        sparse_mask_expanded = sparse_mask.unsqueeze(1).unsqueeze(2)  # [B, 1, 1, T]
        scores = scores.masked_fill(~sparse_mask_expanded, float('-inf'))

        # Apply causal mask if provided
        if attn_mask is not None:
            scores = scores.masked_fill(attn_mask.unsqueeze(0).unsqueeze(0), float('-inf'))

        # Compute attention weights
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)

        # Apply attention to values
        out = torch.matmul(attn_weights, v)
        out = out.transpose(1, 2).contiguous().view(B, T, C)

        return self.out_proj(out)


class SwiGLU(nn.Module):
    """SwiGLU activation function for efficient feed-forward networks"""
    def __init__(self, embed_dim: int, hidden_dim: Optional[int] = None):
        super().__init__()
        hidden_dim = hidden_dim or int(embed_dim * 8/3)  # Standard SwiGLU ratio

        self.gate_proj = nn.Linear(embed_dim, hidden_dim, bias=False)
        self.up_proj = nn.Linear(embed_dim, hidden_dim, bias=False)
        self.down_proj = nn.Linear(hidden_dim, embed_dim, bias=False)

    def forward(self, x: torch.Tensor):
        gate = F.silu(self.gate_proj(x))  # SiLU activation
        up = self.up_proj(x)
        return self.down_proj(gate * up)


class DiffusionBlock(nn.Module):
    """Diffusion-inspired transformer block with denoising capabilities"""
    def __init__(self, embed_dim: int, num_heads: int, dropout: float = 0.1,
                 noise_schedule_steps: int = 1000):
        super().__init__()
        self.embed_dim = embed_dim
        self.noise_schedule_steps = noise_schedule_steps

        # Attention layer with sparse attention
        self.attention = SparseAttention(embed_dim, num_heads, dropout)

        # Feed-forward with SwiGLU
        self.feed_forward = SwiGLU(embed_dim)

        # Layer normalization (pre-norm)
        self.ln1 = nn.LayerNorm(embed_dim)
        self.ln2 = nn.LayerNorm(embed_dim)

        # Noise prediction network
        self.noise_predictor = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 2),
            nn.SiLU(),
            nn.Linear(embed_dim // 2, embed_dim)
        )

        # Time embedding for diffusion steps
        self.time_embedding = nn.Embedding(noise_schedule_steps, embed_dim)

        # Dropout
        self.dropout = nn.Dropout(dropout)

    def add_noise(self, x: torch.Tensor, noise_level: float = 0.1):
        """Add noise to input for diffusion training"""
        noise = torch.randn_like(x) * noise_level
        return x + noise, noise

    def forward(self, x: torch.Tensor, timestep: Optional[int] = None,
                attn_mask: Optional[torch.Tensor] = None):
        # Add time embedding if timestep provided
        if timestep is not None:
            time_emb = self.time_embedding(torch.tensor(timestep, device=x.device))
            x = x + time_emb.unsqueeze(0).unsqueeze(0)

        # Pre-norm attention
        attn_out = self.attention(self.ln1(x), attn_mask)
        x = x + self.dropout(attn_out)

        # Pre-norm feed-forward
        ff_out = self.feed_forward(self.ln2(x))
        x = x + self.dropout(ff_out)

        # Noise prediction for diffusion loss
        predicted_noise = self.noise_predictor(x)

        return x, predicted_noise


class AdvancedTransformerBlock(nn.Module):
    """Advanced transformer block combining sparse attention and diffusion"""
    def __init__(self, embed_dim: int, num_heads: int, dropout: float = 0.1,
                 use_diffusion: bool = True):
        super().__init__()
        self.use_diffusion = use_diffusion

        if use_diffusion:
            self.block = DiffusionBlock(embed_dim, num_heads, dropout)
        else:
            # Standard block with sparse attention and SwiGLU
            self.attention = SparseAttention(embed_dim, num_heads, dropout)
            self.feed_forward = SwiGLU(embed_dim)
            self.ln1 = nn.LayerNorm(embed_dim)
            self.ln2 = nn.LayerNorm(embed_dim)
            self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor, timestep: Optional[int] = None,
                attn_mask: Optional[torch.Tensor] = None):
        if self.use_diffusion:
            return self.block(x, timestep, attn_mask)
        else:
            # Standard transformer block
            attn_out = self.attention(self.ln1(x), attn_mask)
            x = x + self.dropout(attn_out)

            ff_out = self.feed_forward(self.ln2(x))
            x = x + self.dropout(ff_out)

            return x, None  # No predicted noise for standard blocks


# =============================================================================
# IMPROVED GPT-2 MODEL WITH ADVANCED ARCHITECTURE
# =============================================================================

class AdvancedGPT2(nn.Module):
    """Advanced GPT-2 with conditional embeddings, sparse attention, and diffusion blocks"""
    def __init__(self, vocab_size: int, embed_dim: int, num_layers: int,
                 num_heads: int, max_length: int, dropout: float = 0.1,
                 num_tasks: int = 8, num_segments: int = 4,
                 use_diffusion: bool = True, diffusion_ratio: float = 0.5):
        super().__init__()
        self.vocab_size = vocab_size
        self.embed_dim = embed_dim
        self.max_length = max_length
        self.use_diffusion = use_diffusion
        self.num_layers = num_layers

        # Advanced conditional embeddings
        self.embeddings = ConditionalEmbedding(
            vocab_size=vocab_size,
            embed_dim=embed_dim,
            max_length=max_length,
            num_tasks=num_tasks,
            num_segments=num_segments
        )

        self.embed_dropout = nn.Dropout(dropout)

        # Advanced transformer blocks
        self.blocks = nn.ModuleList()
        num_diffusion_blocks = int(num_layers * diffusion_ratio)

        for i in range(num_layers):
            # Use diffusion blocks for the first portion of layers
            use_diffusion_block = i < num_diffusion_blocks and use_diffusion
            block = AdvancedTransformerBlock(
                embed_dim=embed_dim,
                num_heads=num_heads,
                dropout=dropout,
                use_diffusion=use_diffusion_block
            )
            self.blocks.append(block)

        # Output layers
        self.ln_f = nn.LayerNorm(embed_dim)
        self.lm_head = nn.Linear(embed_dim, vocab_size, bias=False)

        # Tie weights between embeddings and output
        self.lm_head.weight = self.embeddings.token_embedding.weight

        # Initialize weights
        self.apply(self._init_weights)

        # Scale embeddings for stability
        with torch.no_grad():
            self.embeddings.token_embedding.weight.mul_(0.01)
            self.embeddings.position_embedding.weight.mul_(0.01)

        print(f"🚀 Advanced GPT-2 initialized: vocab_size={vocab_size}, embed_dim={embed_dim}, "
              f"layers={num_layers}, diffusion_blocks={num_diffusion_blocks}")

    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            # Scaled initialization based on layer depth
            std = 0.02
            if hasattr(module, 'weight'):
                torch.nn.init.normal_(module.weight, mean=0.0, std=std)
            if hasattr(module, 'bias') and module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            if hasattr(module, 'bias'):
                torch.nn.init.zeros_(module.bias)
            if hasattr(module, 'weight'):
                torch.nn.init.ones_(module.weight)

    def forward(self, input_ids: torch.Tensor, targets: Optional[torch.Tensor] = None,
                task_ids: Optional[torch.Tensor] = None, segment_ids: Optional[torch.Tensor] = None,
                use_diffusion_loss: bool = True):
        B, T = input_ids.shape
        device = input_ids.device

        # Advanced conditional embeddings
        x = self.embeddings(input_ids, task_ids, segment_ids)
        x = self.embed_dropout(x)

        # Create causal mask
        causal_mask = torch.triu(torch.ones(T, T, device=device), diagonal=1).bool()

        # Apply advanced transformer blocks
        predicted_noises = []

        for i, block in enumerate(self.blocks):
            # Generate random timestep for diffusion blocks
            timestep = None
            if hasattr(block, 'use_diffusion') and block.use_diffusion:
                timestep = torch.randint(0, 1000, (1,)).item()

            x, predicted_noise = block(x, timestep, causal_mask)

            # Collect diffusion information
            if predicted_noise is not None:
                predicted_noises.append(predicted_noise)

        # Final layer norm
        x = self.ln_f(x)

        # Output projection
        logits = self.lm_head(x)

        # Compute losses
        loss = None

        if targets is not None:
            # Main language modeling loss with label smoothing
            lm_loss = F.cross_entropy(
                logits.view(-1, self.vocab_size),
                targets.view(-1),
                label_smoothing=0.1
            )

            # Diffusion loss (if using diffusion blocks)
            if use_diffusion_loss and predicted_noises:
                diffusion_loss = 0.0
                for predicted_noise in predicted_noises:
                    # Simple noise prediction loss
                    target_noise = torch.randn_like(predicted_noise) * 0.1
                    diffusion_loss += F.mse_loss(predicted_noise, target_noise)
                diffusion_loss /= len(predicted_noises)

                # Combine losses with weighting
                loss = lm_loss + 0.1 * diffusion_loss
            else:
                loss = lm_loss

        return logits, loss
